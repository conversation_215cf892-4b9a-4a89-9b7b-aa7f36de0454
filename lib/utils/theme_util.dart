import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/storage/storage.dart';

/// 高亮模式
ThemeData lightTheme = ThemeData.light().copyWith(

    /// 全局字体样式配置
    textTheme: TextTheme(
      bodyMedium: TextStyle(
          color: Colors.black87, fontWeight: FontWeight.w600, fontSize: 18.sp),
      bodySmall: TextStyle(color: Colors.grey, fontSize: 14.sp),
      bodyLarge: TextStyle(
          color: Colors.blueGrey, fontSize: 14.sp, fontWeight: FontWeight.w400),
    ),
    inputDecorationTheme: const InputDecorationTheme(
        hintStyle: TextStyle(color: Colors.black),
        counterStyle: TextStyle(color: Colors.black),
        enabledBorder:
            UnderlineInputBorder(borderSide: BorderSide(color: Colors.black)),
        // UnderlineInputBorder
        focusedBorder:
            UnderlineInputBorder(borderSide: BorderSide(color: Colors.black))),
    // UnderlineInputBorder, InputDecorationT
    /// 导航栏样式配置
    appBarTheme: const AppBarTheme(
        iconTheme: IconThemeData(color: Colors.black),
        backgroundColor: Colors.white70,
        centerTitle: true,
        elevation: 1.0,
        titleTextStyle: TextStyle(
            fontSize: 18, fontWeight: FontWeight.w600, color: Colors.black)),
    // TextStyle, AppBarTheme
    textButtonTheme: const TextButtonThemeData(),
    splashColor: Colors.transparent,
    highlightColor: Colors.transparent);

/// 暗黑模式
ThemeData darkTheme = ThemeData.dark().copyWith(
  /// 全局字体样式配置
  textTheme: TextTheme(
    bodyMedium: TextStyle(
        color: Colors.white, fontWeight: FontWeight.w600, fontSize: 18.sp),
    bodySmall: TextStyle(color: Colors.white54, fontSize: 14.sp),
    bodyLarge: TextStyle(
        color: Colors.white, fontSize: 15.sp, fontWeight: FontWeight.w400),
  ),
  inputDecorationTheme: const InputDecorationTheme(
      hintStyle: TextStyle(color: Colors.black),
      counterStyle: TextStyle(color: Colors.black),
      enabledBorder:
          UnderlineInputBorder(borderSide: BorderSide(color: Colors.black)),
      // UnderlineInputBorder
      focusedBorder:
          UnderlineInputBorder(borderSide: BorderSide(color: Colors.black))),
  // UnderlineInputBorder, InputDecorationT
  /// 导航栏样式配置
  appBarTheme: const AppBarTheme(
      iconTheme: IconThemeData(color: Colors.white),
      backgroundColor: Colors.black54,
      centerTitle: true,
      elevation: 1.0,
      titleTextStyle: TextStyle(
          fontSize: 18, fontWeight: FontWeight.w600, color: Colors.white)),
  // TextStyle, AppBarTheme
  textButtonTheme: const TextButtonThemeData(),
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
);

class ColorTheme {
  final Map<String, Color> colorMap;

  ColorTheme({
    required this.colorMap,
  });
}

class ThemeColorController extends GetxController {
  /// 当前主题名称
  String currentThemeKey = 'default';

  @override
  void onInit() {
    super.onInit();
    currentThemeKey = _getThemeKey(UserStore.to.deviceTheme);
  }

  final themes = <String, ColorTheme>{
    'default': ColorTheme(
      colorMap: {
        'textColor': const Color.fromRGBO(255, 232, 174, 1),
        'iconColor': const Color.fromRGBO(255, 211, 9, 1),
        'conBgColor': const Color.fromRGBO(238, 238, 238, 1),
        'navColor': const Color.fromRGBO(255, 249, 235, 0.9),
        'conBrodeColor': const Color.fromRGBO(240, 220, 105, 1),
        'diaryRecord': const Color.fromRGBO(180, 142, 78, 1),
        'gradientStar': const Color.fromRGBO(241, 253, 255, 1),
        'gradientEnd': const Color.fromRGBO(222, 250, 255, 0),
        'cupSchedule': const Color.fromRGBO(43, 210, 247, 1),
        'sliderBoxShadowColor': const Color.fromRGBO(123, 228, 255, 0.39),
        'sliderInactiveColor': const Color.fromRGBO(214, 241, 248, 1),
        'sliderActiveColor': const Color.fromRGBO(72, 222, 249, 1),
        'gWaterBgColor': const Color.fromRGBO(241, 240, 242, 1),
        'gEmptyWaterBgColor': const Color.fromRGBO(241, 240, 242, 1),
        'waterFullColor': const Color.fromRGBO(61, 217, 248, 1),
        'chartGradientStar': const Color.fromRGBO(97, 235, 252, 1),
        'chartGradientEnd': const Color.fromRGBO(50, 211, 247, 1),
        'chartCupColor': const Color.fromRGBO(20, 191, 222, 1),
        'waterTips': const Color.fromRGBO(76, 179, 255, 1),
        'skinBorderColor': const Color.fromRGBO(255, 211, 9, 1),
        'skinFullBgColor': const Color.fromRGBO(233, 186, 106, 0.23),
        'cancelInactiveFill': const Color.fromRGBO(255, 232, 174, 0.26),
        'cancelSelectedFill': const Color.fromRGBO(255, 232, 174, 0.26),
        'cancelSelected': const Color.fromRGBO(255, 211, 78, 1),
        'cancelActiveFill': const Color.fromRGBO(255, 232, 174, 0.26),
        'drinkRecord': const Color.fromRGBO(43, 210, 247, 1),
        'diaryCircle': const Color.fromRGBO(255, 211, 9, 1),
        'mineOnline': const Color.fromRGBO(180, 142, 78, 1),
        'mineBg02': const Color.fromRGBO(255, 220, 199, 1),
        'floatingTextColor': const Color.fromRGBO(180, 142, 78, 1),
        'conBoxShadowColor': const Color.fromRGBO(236, 228, 204, 0.53),
        'consoleSoundUseBtn': const Color.fromRGBO(255, 232, 174, 0.44),
        'consoleDefalutColor': const Color.fromRGBO(69, 47, 10, 1),
        'rechargeGradientOne': const Color.fromRGBO(255, 245, 215, 1),
        'rechargeGradientTwo': const Color.fromRGBO(255, 232, 174, 1),
        'scheduleItemColor': const Color.fromRGBO(255, 248, 230, 1),
        'periodSwitchColor': const Color.fromRGBO(51, 51, 51, 1)
      },
    ),
    'pink': ColorTheme(colorMap: {
      'textColor': const Color.fromRGBO(255, 220, 224, 1),
      'iconColor': const Color.fromRGBO(255, 139, 172, 1),
      'conBgColor': const Color.fromRGBO(255, 237, 239, 1),
      'navColor': const Color.fromRGBO(255, 241, 242, 0.9),
      'conBrodeColor': const Color.fromRGBO(255, 139, 172, 1),
      'diaryRecord': const Color.fromRGBO(255, 139, 172, 1),
      'gradientStar': const Color.fromRGBO(255, 241, 245, 1),
      'gradientEnd': const Color.fromRGBO(255, 222, 237, 0),
      'cupSchedule': const Color.fromRGBO(255, 139, 172, 1),
      'sliderBoxShadowColor': const Color.fromRGBO(255, 220, 224, 1),
      'sliderInactiveColor': const Color.fromRGBO(255, 220, 224, 1),
      'sliderActiveColor': const Color.fromRGBO(253, 155, 193, 1),
      'gWaterBgColor': const Color.fromRGBO(255, 253, 253, 1),
      'gEmptyWaterBgColor': const Color.fromRGBO(241, 240, 242, 1),
      'waterFullColor': const Color.fromRGBO(254, 139, 172, 1),
      'chartGradientStar': const Color.fromRGBO(252, 175, 197, 1),
      'chartGradientEnd': const Color.fromRGBO(254, 139, 172, 1),
      'chartCupColor': const Color.fromRGBO(254, 139, 172, 1),
      'waterTips': const Color.fromRGBO(254, 139, 172, 1),
      'skinBorderColor': const Color.fromRGBO(254, 139, 172, 1),
      'skinFullBgColor':
          const Color.fromRGBO(254, 139, 172, 0.64).withValues(alpha: 0.23),
      'cancelInactiveFill': const Color.fromRGBO(255, 220, 224, 0.26),
      'cancelSelectedFill': const Color.fromRGBO(255, 220, 224, 0.26),
      'cancelSelected': const Color.fromRGBO(254, 139, 172, 1),
      'cancelActiveFill': const Color.fromRGBO(255, 220, 224, 0.26),
      'drinkRecord': const Color.fromRGBO(255, 139, 172, 1),
      "diaryCircle": const Color.fromRGBO(255, 139, 172, 1),
      'mineOnline': const Color.fromRGBO(255, 139, 172, 1),
      'mineBg02': const Color.fromRGBO(255, 220, 224, 1),
      'floatingTextColor': const Color.fromRGBO(255, 139, 172, 1),
      'conBoxShadowColor': const Color.fromRGBO(236, 204, 204, 0.53),
      'consoleSoundUseBtn': const Color.fromRGBO(255, 220, 224, 1),
      'consoleDefalutColor': const Color.fromRGBO(255, 139, 172, 1),
      'rechargeGradientOne': const Color.fromRGBO(254, 139, 172, 1),
      'rechargeGradientTwo': const Color.fromRGBO(252, 175, 197, 1),
      'scheduleItemColor': const Color.fromRGBO(255, 243, 244, 1),
      'periodSwitchColor': const Color.fromRGBO(254, 139, 172, 1),
    })
  };

  ///当前主题对象
  ColorTheme get currentTheme => themes[currentThemeKey]!;

  ///更改主题key
  void switchTheme(int theme) {
    String themeKey = _getThemeKey(theme);
    if (themes.containsKey(themeKey) && themeKey != currentThemeKey) {
      currentThemeKey = themeKey;
      update(["theme"]);
    }
  }

  ///获取当前对应的颜色
  Color getColor(String key, {Color fallBack = Colors.transparent}) {
    return currentTheme.colorMap[key] ?? fallBack;
  }

  Color get gTextColor => getColor("textColor");
  Color get gIconColor => getColor("iconColor");

  Color get gFloatingTextColor => getColor("floatingTextColor");

  ///导航栏颜色
  Color get gNavColor => getColor("navColor");

  ///边框颜色
  Color get gConBrodeColor => getColor("conBrodeColor");

  ///==============小耙======================
  Color get gConBoxShadowColor => getColor("conBoxShadowColor");

  ///==============心情相关==================
  Color get gDiaryRecord => getColor("diaryRecord");
  Color get gDiaryCircle => getColor("diaryCircle");

  ///=============喝水界面========================
  Color get gGradientStar => getColor("gradientStar");
  Color get gGradientEnd => getColor("gradientEnd");
  Color get gCupSchedule => getColor('cupSchedule');
  Color get gSliderBoxShadowColor => getColor('sliderBoxShadowColor');
  Color get gSliderInactiveColor => getColor('sliderInactiveColor');
  Color get gSliderActiveColor => getColor('sliderActiveColor');
  Color get gWaterBgColor => getColor('gWaterBgColor');
  Color get gEmptyWaterBgColor => getColor('gEmptyWaterBgColor');
  Color get gWaterFullColor => getColor('waterFullColor');
  Color get gChartGradientStar => getColor('chartGradientStar');
  Color get gChartGradientEnd => getColor('chartGradientEnd');
  Color get gChartCupColor => getColor('chartCupColor');
  Color get gWaterTips => getColor('waterTips');
  Color get gDrinkRecord => getColor('drinkRecord');

  ///=================皮肤界面========================
  Color get gSkinBorderColor => getColor('skinBorderColor');
  Color get gSkinFullBgColor => getColor('skinFullBgColor');

  ///==============注销账号界面=========================
  Color get gCancelInactiveFill => getColor('cancelInactiveFill');
  Color get gCancelSelectedFill => getColor('cancelSelectedFill');
  Color get gCancelSelected => getColor('cancelSelected');
  Color get gCancelActiveFill => getColor('cancelActiveFill');

  ///==============克隆声音界面=========================
  Color get gConsoleSoundUseBtn => getColor('consoleSoundUseBtn');
  Color get gConsoleDefalutColor => getColor('consoleDefalutColor');

  Color get gMineOnline => getColor('mineOnline');

  ///===============充值界面==========================
  Color get gRechargeGradientOne => getColor('rechargeGradientOne');
  Color get gRechargeGradientTwo => getColor('rechargeGradientTwo');

  ///===============经期界面===========================
  Color get gPeriodSwitchColor => getColor('periodSwitchColor');

  String _getThemeKey(int index) {
    switch (index) {
      case 1:
        return 'default';
      case 2:
        return 'pink';
      default:
        return 'default';
    }
  }
}

class ThemeImageController extends GetxController {
  /// 当前主题名称
  String currentThemeKey = 'default';

  @override
  void onInit() {
    super.onInit();
    currentThemeKey = _getThemeKey(UserStore.to.deviceTheme);
  }

  ///当前主题对象
  String get baseImage => 'assets/images';

  ///更改主题key
  void switchTheme(int theme) {
    String themeKey = _getThemeKey(theme);
    if (themeKey != currentThemeKey) {
      currentThemeKey = themeKey;
      update(["theme"]);
    }
  }

  ///获取当前对应的图片路径
  String getImagePath(String fileName, {String fallBack = ''}) {
    String path = '$baseImage/$currentThemeKey/$fileName';
    return path;
  }

  String get taskImage01 => getImagePath('task_image_01.png');
  String get taskShareBg01 => getImagePath('task_share_bg_01.png');
  String get taskShareBg02 => getImagePath('task_share_bg_02.png');

  String get drinkBg => getImagePath('drink_bg.png');

  String get soundExchangeBg => getImagePath('sound_exchange_bg.png');
  String get exchangeResultImage => getImagePath('exchange_result_image.png');

  String get soundBuyBtnBg => getImagePath('sound_buy_btn_bg.png');
  String get soundBuyBtnBgEn => getImagePath('sound_buy_btn_bg_en.png');

  String get onlineJingYin => getImagePath('console_bind_jingyin.png');
  String get onlineXiuMian => getImagePath('console_bind_xiumian.png');

  String get compiledImage01 => getImagePath('compiled_image_01.png');
  String get compiledImage02 => getImagePath('compiled_image_02.png');
  String get compiledImage03 => getImagePath('compiled_image_03.png');

  String _getThemeKey(int index) {
    switch (index) {
      case 1:
        return 'default';
      case 2:
        return 'pink';
      default:
        return 'default';
    }
  }
}
