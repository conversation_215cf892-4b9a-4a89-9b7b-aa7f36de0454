import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'index.dart';

class SoundExchangePage extends BaseCommonView<SoundExchangeController> {
  SoundExchangePage({super.key});

  @override
  String? get navTitle => "console_sound_exchange".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w600);

  @override
  Color? get navColor => Colors.transparent;

  @override
  double? get leftWidth => 35.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  @override
  bool? get extendBodyBehindAppBar => true;

  @override
  Color? get contentColor => Colors.white;

  // 主视图
  Widget _buildView() {
    return SizedBox(
      width: 1.sw,
      height: 1.sh,
      child: Stack(
        children: [
          Images(
            path: Get.find<ThemeImageController>().soundExchangeBg,
            width: 1.sw,
            height: 273.h,
            boxFit: BoxFit.fill,
          ),
          Positioned(
            top: 259.h,
            child: Container(
              padding: EdgeInsets.only(top: 30.h, left: 22.w, right: 22.w),
              width: 1.sw,
              height: 553.h,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16.r),
                  topRight: Radius.circular(16.r),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'console_sound_exchange_view_one'.tr,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor,
                        fontWeight: FontWeight.w600),
                  ),
                  Container(
                    margin: EdgeInsets.only(top: 12.h),
                    width: 331.w,
                    height: 30.h,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(0.r),
                      color: Colors.transparent,
                    ),
                    child: Column(
                      children: [
                        Expanded(
                          child: AppTextField(
                            controller: controller.ticketController,
                            style: StyleConfig.otherStyle(
                                color: ColorConfig.searchTextColor,
                                fontSize: 14,
                                height: 1,
                                fontWeight: FontWeight.w500),
                            keyboardType: TextInputType.text,
                            maxLength: 17,
                            decoration: InputDecoration(
                                border: InputBorder.none,
                                enabledBorder: InputBorder.none,
                                focusedBorder: InputBorder.none,
                                counterText: '',
                                hintText:
                                    'console_sound_exchange_view_hint_text'.tr,
                                hintStyle: StyleConfig.otherStyle(
                                    color: ColorConfig.shopDetailTextColor,
                                    fontSize: 14,
                                    height: 1,
                                    fontWeight: FontWeight.w500)),
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.only(top: 10.h),
                          width: 331.w,
                          height: 1.h,
                          color: const Color.fromRGBO(233, 232, 233, 1),
                        )
                      ],
                    ),
                  ),
                  SizedBox(height: 30.h),
                  Text(
                    'console_sound_exchange_view_title'.tr,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.authenticationTextColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w600),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    'console_sound_exchange_view_tips'.tr,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.shopDetailTextColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w500),
                  ),
                  const Expanded(child: SizedBox()),
                  Container(
                    margin: EdgeInsets.only(
                        left: 46.w,
                        right: 46.w,
                        bottom: ScreenUtil().statusBarHeight + 30.h),
                    width: 283.w,
                    height: 48.h,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(24.r),
                        color: ColorConfig.searchTextColor),
                    child: Center(
                      child: ThemeText(
                        dataStr: "console_sound_exchange_view_btn".tr,
                        keyName: 'textColor',
                        fontSize: 16,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ).inkWell(() => controller.exchangeAction())
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  @override
  Widget buildContent() {
    return GetBuilder<SoundExchangeController>(
      init: SoundExchangeController(),
      id: "sound_exchange",
      builder: (_) {
        return _buildView();
      },
    );
  }
}
