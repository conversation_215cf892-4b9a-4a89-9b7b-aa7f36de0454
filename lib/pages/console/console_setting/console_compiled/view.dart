import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'index.dart';

class ConsoleCompiledPage extends BaseCommonView {
  ConsoleCompiledPage({super.key});

  final ConsoleCompiledController logic = Get.put(ConsoleCompiledController());

  @override
  String? get navTitle => "console_compiled_title".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w600);

  @override
  Color? get navColor => Colors.transparent;

  @override
  double? get leftWidth => 35.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  @override
  Color? get contentColor => const Color.fromRGBO(249, 249, 249, 1);

  // 主视图
  Widget _buildView() {
    return Column(
      children: [
        oneWidget(),
        SizedBox(height: 10.h),
        SizedBox(
          width: 345.w,
          child: Text(
            "console_compiled_second_item_three_tips_two".tr,
            style: StyleConfig.otherStyle(
                color: ColorConfig.shopDetailTextColor,
                fontWeight: FontWeight.w400,
                fontSize: 12),
          ),
        ),
        Offstage(
          offstage: logic.type == 0,
          child: _twoWidget(),
        ),
      ],
    );
  }

  ///
  Widget oneWidget() {
    return Container(
      margin: EdgeInsets.only(top: 10.h),
      padding:
          EdgeInsets.only(left: 14.w, right: 14.w, top: 16.h, bottom: 20.h),
      width: 345.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            // 阴影颜色和透明度
            spreadRadius: 0,
            // 阴影扩散范围
            blurRadius: 10,
            // 阴影模糊程度
            offset: const Offset(0, 2),
            // 阴影偏移量（水平，垂直）
          )
        ],
      ),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Text(
          "console_compiled_second_title".tr,
          style: StyleConfig.otherStyle(
            color: ColorConfig.searchTextColor,
            fontWeight: FontWeight.w700,
          ),
        ),
        SizedBox(height: 4.h),
        SizedBox(
          width: 270.w,
          child: Text(
            "console_compiled_second_tips".tr,
            style: StyleConfig.otherStyle(
                color: ColorConfig.shopDetailTextColor,
                fontWeight: FontWeight.w400,
                fontSize: 12),
          ),
        ),
        SizedBox(height: 16.h),
        ...List.generate(logic.compiledType.length, (index) {
          return Container(
            margin: EdgeInsets.only(top: index == 0 ? 0.h : 12.h),
            padding: EdgeInsets.only(bottom: 10.h),
            constraints: BoxConstraints(minHeight: 81.h),
            width: 315.w,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6.r),
                color: const Color.fromRGBO(249, 249, 249, 1),
                border: logic.compiledType[index].isSelect
                    ? Border.all(
                        width: 2.r, color: const Color.fromRGBO(255, 211, 9, 1))
                    : null),
            child: Row(
              children: [
                SizedBox(width: 14.w),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 15.h),
                    Text(
                      logic.compiledType[index].title,
                      style: StyleConfig.otherStyle(
                          color: logic.compiledType[index].isSelect
                              ? Get.find<ThemeColorController>().gMineOnline
                              : const Color.fromRGBO(69, 67, 74, 1),
                          fontSize: 14,
                          fontWeight: FontWeight.w600),
                    ),
                    SizedBox(height: 6.h),
                    SizedBox(
                      width: 200.w,
                      child: Text(
                        logic.compiledType[index].content,
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.shopDetailTextColor,
                            fontSize: 12,
                            fontWeight: FontWeight.w400),
                      ),
                    )
                  ],
                ),
                Images(
                  path: logic.compiledType[index].image,
                  width: 96.w,
                  height: 77.h,
                  boxFit: BoxFit.fill,
                )
              ],
            ),
          ).inkWell(() => logic.typeAction(index));
        })
      ]),
    );
  }

  ///第三个控件
  Widget _twoWidget() {
    return Column(
      children: [
        Container(
          margin: EdgeInsets.only(top: 10.h),
          padding:
              EdgeInsets.only(left: 14.w, right: 14.w, top: 12.h, bottom: 14.h),
          width: 345.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.r),
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.06),
                // 阴影颜色和透明度
                spreadRadius: 0,
                // 阴影扩散范围
                blurRadius: 10,
                // 阴影模糊程度
                offset: const Offset(0, 2),
                // 阴影偏移量（水平，垂直）
              )
            ],
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Text(
                    "console_compiled_third_title".tr,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor,
                        fontWeight: FontWeight.w600),
                  ),
                  const Expanded(child: SizedBox()),
                  CupertinoSwitch(
                      activeTrackColor: ColorConfig.searchTextColor,
                      value: logic.isDisturb,
                      onChanged: (value) => logic.isDisturbAction(value)),
                ],
              ),
              Offstage(
                  offstage: !logic.isDisturb,
                  child: Container(
                    margin: EdgeInsets.only(top: 12.h),
                    child: Row(
                      children: [
                        Container(
                          padding: EdgeInsets.only(left: 20.w),
                          width: 110.w,
                          height: 67.h,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(7.r),
                            color: const Color.fromRGBO(249, 249, 249, 1),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                logic.startTime,
                                style: StyleConfig.otherStyle(
                                    color: ColorConfig.searchTextColor,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400),
                              ),
                              SizedBox(height: 2.h),
                              Text(
                                logic.startTips,
                                style: StyleConfig.otherStyle(
                                    color: ColorConfig.shopDetailTextColor,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400),
                              ),
                            ],
                          ),
                        ).inkWell(() => logic.startTimeAction()),
                        SizedBox(width: 12.w),
                        Text(
                          "~",
                          style: StyleConfig.otherStyle(
                              color: ColorConfig.authenticationTextColor,
                              fontSize: 12,
                              fontWeight: FontWeight.w400),
                        ),
                        SizedBox(width: 12.w),
                        Container(
                          padding: EdgeInsets.only(left: 20.w),
                          width: 110.w,
                          height: 67.h,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(7.r),
                            color: const Color.fromRGBO(249, 249, 249, 1),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                logic.endTime,
                                style: StyleConfig.otherStyle(
                                    color: ColorConfig.searchTextColor,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400),
                              ),
                              SizedBox(height: 2.h),
                              Text(
                                logic.endTips,
                                style: StyleConfig.otherStyle(
                                    color: ColorConfig.shopDetailTextColor,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400),
                              ),
                            ],
                          ),
                        ).inkWell(() => logic.endTimeAction()),
                      ],
                    ),
                  ))
            ],
          ),
        ),
        SizedBox(height: 10.h),
        SizedBox(
          width: 345.w,
          child: Text(
            "console_compiled_third_tips".tr,
            style: StyleConfig.otherStyle(
                color: ColorConfig.shopDetailTextColor,
                fontWeight: FontWeight.w400,
                fontSize: 12),
          ),
        ),
      ],
    );
  }

  @override
  Widget buildContent() {
    return GetBuilder<ConsoleCompiledController>(
      id: "console_compiled_view",
      builder: (_) {
        return Container(
          margin: EdgeInsets.only(
              top: 10.h,
              left: 15.w,
              right: 15.w,
              bottom: ScreenUtil().bottomBarHeight + 20.h),
          width: 1.sw,
          height: 1.sh,
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: _buildView(),
                ),
              ),
              Container(
                margin: EdgeInsets.only(top: 20.h),
                width: 283.w,
                height: 48.h,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24.r),
                    color: const Color.fromRGBO(40, 39, 46, 1)),
                child: Center(
                  child: ThemeText(
                    dataStr: "console_compiled_save".tr,
                    keyName: 'textColor',
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ).inkWell(() => logic.saveAction())
            ],
          ),
        );
      },
    );
  }
}
