import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/custom_shimmer.dart';
import 'package:getx_xiaopa/widget/theme_text.dart';

import 'console_update_logic.dart';
import 'console_update_state.dart';

class ConsoleUpdatePage extends BaseCommonView {
  ConsoleUpdatePage({super.key});

  final ConsoleUpdateLogic logic = Get.put(ConsoleUpdateLogic());
  final ConsoleUpdateState state = Get.find<ConsoleUpdateLogic>().state;

  @override
  String? get navTitle => "console_update".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w500);

  @override
  Color? get navColor => Colors.transparent;

  @override
  double? get leftWidth => 35.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  @override
  Color? get contentColor => const Color.fromRGBO(249, 249, 249, 1);

  @override
  Widget buildContent() {
    return GetBuilder<ConsoleUpdateLogic>(
      id: "console_update_view",
      builder: (_) => createCommonView(
        logic,
        (_) {
          return SizedBox(
            width: 1.sw,
            child: Column(
              children: [
                Container(
                  padding: EdgeInsets.only(left: 20.w, right: 20.w),
                  width: 345.w,
                  height: 50.h,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      color: Colors.white),
                  child: Row(
                    children: [
                      Text(
                        "console_update_auto".tr,
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.searchTextColor),
                      ),
                      const Expanded(child: SizedBox()),
                      CupertinoSwitch(
                          activeTrackColor: ColorConfig.searchTextColor,
                          value: state.isAuto,
                          onChanged: (value) => logic.autoModel(value)),
                    ],
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: 10.h),
                  padding: EdgeInsets.only(left: 20.w, right: 20.w),
                  width: 345.w,
                  height: 50.h,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(10.r),
                          topRight: Radius.circular(10.r),
                          bottomLeft: Radius.circular(state.isAuto ? 10.r : 0),
                          bottomRight:
                              Radius.circular(state.isAuto ? 10.r : 0)),
                      color: Colors.white),
                  child: Row(
                    children: [
                      Text(
                        state.versionTis,
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.searchTextColor),
                      ),
                      const Expanded(child: SizedBox()),
                      Text(
                        "v${state.currentVersion}",
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.shopDetailTextColor),
                      ),
                    ],
                  ),
                ),
                Offstage(
                  offstage: state.isAuto,
                  child: Container(
                    padding: EdgeInsets.only(
                        left: 20.w,
                        right: 20.w,
                        bottom:
                            state.versionDeviceModel.logs!.isEmpty ? 0 : 20.h),
                    width: 345.w,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(state.isAuto ? 10.r : 0),
                            topRight: Radius.circular(state.isAuto ? 10.r : 0),
                            bottomLeft: Radius.circular(10.r),
                            bottomRight: Radius.circular(10.r)),
                        color: Colors.white),
                    child: Column(
                      children: [
                        Container(
                          width: 295.w,
                          height: 1.h,
                          color: const Color.fromRGBO(233, 232, 233, 1),
                        ),
                        Container(
                          padding: EdgeInsets.only(top: 20.h),
                          width: 345.w,
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  Text(
                                    "${"console_update_new_version".tr}：v${state.versionDeviceModel.code}",
                                    style: StyleConfig.otherStyle(
                                        color: ColorConfig.searchTextColor),
                                  ),
                                  const Expanded(child: SizedBox()),
                                  Offstage(
                                    offstage: state.isUpdate,
                                    child: Container(
                                      width: 88.w,
                                      height: 29.h,
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(15.r),
                                          color: const Color.fromRGBO(
                                              40, 39, 46, 1)),
                                      child: Center(
                                          child: ThemeText(
                                        dataStr: "console_update_now".tr,
                                        keyName: 'textColor',
                                        fontSize: 12,
                                      )),
                                    ).inkWell(() => logic.updateNow()),
                                  )
                                ],
                              ),
                              SizedBox(
                                  height:
                                      (state.versionDeviceModel.logs!.isEmpty &&
                                              state.versionDeviceModel.logsEn!
                                                  .isEmpty)
                                          ? 0
                                          : 10.h),
                              SizedBox(
                                width: 304.w,
                                child: Text(
                                  ConfigStore.to.getLocaleCode() == 'zh'
                                      ? (state.versionDeviceModel.logs ?? '')
                                      : (state.versionDeviceModel.logsEn ?? ''),
                                  style: StyleConfig.otherStyle(
                                      color: ColorConfig.searchTextColor,
                                      fontSize: 14),
                                ),
                              )
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                )
              ],
            ),
          );
        },
        initBuilder: () => Column(
          children: [
            CustomShimmer(
              margin: 10,
              width: 1.sw,
              height: 50.h,
            ),
            CustomShimmer(
              margin: 10,
              width: 1.sw,
              height: 50.h,
            )
          ],
        ),
      ),
    );
  }
}
