import 'dart:collection';

import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/request_body.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/console/console_schedule/model/schedule_model_result.dart';
import 'package:getx_xiaopa/storage/user_store.dart';
import 'package:getx_xiaopa/utils/utils.dart';

class ScheduleStore extends GetxService {
  static ScheduleStore get to => Get.find();

  final DateTime _toDay = DateTime.now();

  Map<String, List<ScheduleModel>> _scheduleTodayMap = {};
  Map<String, List<ScheduleModel>> get scheduleTodayMap => _scheduleTodayMap;

  ///主要是用来统计多少条
  List<ScheduleModel> _schedulePlanList = [];
  List<ScheduleModel> get schedulePlanList => _schedulePlanList;

  Map<String, List<ScheduleModel>> _schedulePlanMap = {};
  Map<String, List<ScheduleModel>> get schedulePlanMap => _schedulePlanMap;

  Map<String, List<ScheduleModel>> _scheduleAllMap = {};
  Map<String, List<ScheduleModel>> get scheduleAllMap => _scheduleAllMap;

  init() async {
    getTodaySchedule(time: _toDay);
    getNoRepeatSchedule();
    getAllSchedule();
  }

  ///获取今天的数据
  Future getTodaySchedule({required DateTime time}) async {
    var map = RequestBody(pagination: false, filters: [
      Filter(
          expr: "=",
          name: "device_id",
          value: UserStore.to.deviceList[UserStore.to.selectDevice].id),
      Filter(expr: "between", name: "next_active_at", value: _getToDay(time))
    ], orders: [
      Order(expr: "asr", name: "next_active_at")
    ]).toJson();
    Result result = await http.itemsSchedule(map);
    if (result.code == 0) {
      _scheduleTodayMap.clear();

      ScheduleModelResult mResult = result.data;

      for (ScheduleModel model in mResult.data!.whereType<ScheduleModel>()) {
        DateTime isoTime = TimeUtil.parseIsoDate(model.nextActiveAt!);
        String hmStr = TimeUtil.getSimpleHM(isoTime.toString());
        if (!_scheduleTodayMap.keys.contains(hmStr)) {
          _scheduleTodayMap[hmStr] = [];
        }
        _scheduleTodayMap[hmStr]!.add(model);
      }
    } else {
      CToast.showToast(result.msg!);
    }
  }

  _getToDay(DateTime date) {
    return [
      "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')} 00:00:00",
      "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')} 23:59:59"
    ];
  }

  ///获取计划的数据
  Future getNoRepeatSchedule() async {
    var map = RequestBody(pagination: false, filters: [
      Filter(
          expr: "=",
          name: "device_id",
          value: UserStore.to.deviceList[UserStore.to.selectDevice].id),
      Filter(expr: ">", name: "next_active_at", value: _getToDate()),
      Filter(expr: "=", name: "repeat_rule", value: "0000000")
    ], orders: [
      Order(expr: "asr", name: "next_active_at")
    ]).toJson();
    Result result = await http.itemsSchedule(map);
    if (result.code == 0) {
      _schedulePlanMap.clear();
      _schedulePlanList.clear();
      await _getRepeatSchedule(result.data.data);
    } else {
      CToast.showToast(result.msg!);
    }
  }

  Future _getRepeatSchedule(List<ScheduleModel> temp) async {
    var map = RequestBody(pagination: false, filters: [
      Filter(
          expr: "=",
          name: "device_id",
          value: UserStore.to.deviceList[UserStore.to.selectDevice].id),
      Filter(expr: "<>", name: "repeat_rule", value: "0000000"),
      Filter(expr: ">=", name: 'next_active_at', value: _getToCurrentTiem())
    ], orders: [
      Order(expr: "asr", name: "next_active_at")
    ]).toJson();
    Result result = await http.itemsSchedule(map);
    if (result.code == 0) {
      List<ScheduleModel> tempList = temp;
      tempList.addAll(result.data.data);
      _schedulePlanList = tempList;
      for (ScheduleModel m in tempList) {
        String activeDate =
            TimeUtil.getSimpleDate(m.nextActiveAt, isHMS: false);
        if (!_schedulePlanMap.containsKey(activeDate)) {
          _schedulePlanMap[activeDate] = [];
        }
        _schedulePlanMap[activeDate]!.add(m);
      }
      _schedulePlanMap = _sortMap(_schedulePlanMap);
    } else {
      CToast.showToast(result.msg!);
    }
  }

  _getToCurrentTiem() {
    DateTime date = DateTime.now();
    String time =
        '${date.hour.toString().padLeft(2, "0")}:${date.minute.toString().padLeft(2, "0")}:${date.second.toString().padLeft(2, "0")}';
    return [
      "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')} $time",
    ];
  }

  _getToDate() {
    DateTime date = DateTime.now();
    DateTime nextDate = date.add(const Duration(days: 1));
    return "${nextDate.year}-${nextDate.month.toString().padLeft(2, '0')}-${nextDate.day.toString().padLeft(2, '0')} 00:00:00";
  }

  ///返回值 true=还有数据，可以继续上拉加载
  getAllSchedule({DateTime? signDate}) async {
    signDate ??= DateTime.now();

    var map = RequestBody(pagination: false, filters: [
      Filter(
          expr: "=",
          name: "device_id",
          value: UserStore.to.deviceList[UserStore.to.selectDevice].id),
      Filter(
          expr: "between",
          name: "next_active_at",
          value: _getWeekStartAndEndDate(signDate))
    ], orders: [
      Order(expr: "asr", name: "next_active_at")
    ]).toJson();
    Result result = await http.itemsSchedule(map);
    if (result.code == 0) {
      _scheduleAllMap.clear();
      for (ScheduleModel m in result.data.data) {
        String activeDate =
            TimeUtil.getSimpleDate(m.nextActiveAt, isHMS: false);
        if (!_scheduleAllMap.containsKey(activeDate)) {
          _scheduleAllMap[activeDate] = [];
        }
        _scheduleAllMap[activeDate]!.add(m);
      }

      _scheduleAllMap = _sortMap(_scheduleAllMap);
    } else {
      CToast.showToast(result.msg!);
    }
  }

  /// 获取传入日期的本周第一天和最后一天（周日开始）
  _getWeekStartAndEndDate(DateTime date) {
    // 获取本周的第一天（周日）
    final firstDayOfWeek = date.subtract(Duration(days: date.weekday % 7));
    // 获取本周的最后一天（周六）
    final lastDayOfWeek = firstDayOfWeek.add(const Duration(days: 6));

    // 将起始和结束时间都往后推一天
    final shiftedStart = firstDayOfWeek.add(const Duration(days: 0));
    final shiftedEnd = lastDayOfWeek.add(const Duration(days: 0));

    return [
      "${shiftedStart.year}-${shiftedStart.month.toString().padLeft(2, '0')}-${shiftedStart.day.toString().padLeft(2, '0')} 00:00:00",
      "${shiftedEnd.year}-${shiftedEnd.month.toString().padLeft(2, '0')}-${shiftedEnd.day.toString().padLeft(2, '0')} 23:59:59"
    ];
  }

  //按 key 升序
  Map<String, List<ScheduleModel>> _sortMap(
      Map<String, List<ScheduleModel>> map) {
    final sortedMap = SplayTreeMap<String, List<ScheduleModel>>.from(
      map,
      (a, b) => a.compareTo(b), // 按 key 升序
    );
    return sortedMap;
  }
}
