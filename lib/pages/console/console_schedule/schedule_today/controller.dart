import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/console/console_schedule/index.dart';
import 'package:getx_xiaopa/pages/console/console_schedule/model/schedule_model_result.dart';
import 'package:getx_xiaopa/routes/router.dart';

class ScheduleTodayController extends GetxController {
  ScheduleTodayController();

  bool showDate = false;
  DateTime _selectDay = DateTime.now();
  String year = '';
  String monthAndDay = '';
  final DateTime _nowDay = DateTime.now();
  String today = 'console_schedule_today'.tr;

  String iconImage = 'schedule_today_down.png';

  Map<String, List<ScheduleModel>> scheduleMap = {};

  @override
  void onReady() {
    super.onReady();
    _getDateStr();

    scheduleMap = ScheduleStore.to.scheduleTodayMap;

    update(["schedule_today"]);
  }

  _getDateStr() {
    year = _selectDay.year.toString();
    monthAndDay =
        '${_selectDay.month.toString().padLeft(2, '0')}.${_selectDay.day.toString().padLeft(2, '0')}';
  }

  showDateAction() {
    showDate = !showDate;
    if (showDate) {
      iconImage = 'schedule_today_up.png';
    } else {
      iconImage = 'schedule_today_down.png';
    }
    update(["schedule_today"]);
  }

  selectDateAction(DateTime date) async {
    _selectDay = date;
    bool flag = _isSameDay(date);
    if (flag) {
      today = 'console_schedule_today'.tr;
    } else {
      today = '';
    }

    _getDateStr();

    _requestTodayData();
  }

  bool _isSameDay(DateTime b) {
    return _nowDay.year == b.year &&
        _nowDay.month == b.month &&
        _nowDay.day == b.day;
  }

  toAddSchedulePage() {
    Get.toNamed(AppRoutes.SCHEDULE_ADD)?.then((value) async {
      if (value == "success") {
        _requestTodayData();
      }
    });
  }

  removeSchedule(String value) {
    _removeSchedule(scheduleId: value);
  }

  ///=====================网络请求===============================
  _requestTodayData() async {
    await ScheduleStore.to.getTodaySchedule(time: _selectDay);
    scheduleMap = ScheduleStore.to.scheduleTodayMap;
    update(["schedule_today"]);
  }

  _removeSchedule({required String scheduleId}) async {
    CToast.showLoading();
    var map = {"id": scheduleId};
    Result result = await http.rmoveSchedule(map);
    if (result.code == 0) {
      _requestTodayData();
    } else {
      CToast.showToast(result.msg!);
    }
  }
}
