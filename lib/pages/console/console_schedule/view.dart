import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/console/console_schedule/widgets/schedule_item.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'index.dart';

class ConsoleSchedulePage extends BaseCommonView<ConsoleScheduleController> {
  ConsoleSchedulePage({super.key});

  @override
  String? get navTitle => "console_schedule_title".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w600);

  @override
  Color? get navColor => Colors.transparent;

  @override
  double? get leftWidth => 35.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  @override
  Color? get contentColor => const Color.fromRGBO(249, 249, 249, 1);

  // 主视图
  Widget _buildView() {
    return Container(
      margin: EdgeInsets.only(top: 13.h, left: 15.w, right: 15.w),
      child: Column(
        children: [
          ScheduleItem(
              icon: ThemeContainerImage(
                fileName: "console_schedule_item_001.png",
                conWidget: 44.r,
                conHeight: 44.r,
                fit: BoxFit.fill,
                padding: EdgeInsets.only(top: 16.h, left: 14.w),
                child: ThemeText(
                  dataStr: controller.dateDay,
                  keyName: 'iconColor',
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
              title: "console_schedule_one".tr,
              subWidget: Text(
                '${ScheduleStore.to.scheduleTodayMap.keys.length}',
                style: StyleConfig.otherStyle(
                    color: ColorConfig.searchTextColor,
                    fontSize: 24,
                    fontWeight: FontWeight.w900),
              ),
              onClick: () => controller.toScheduleTodayPage()),
          SizedBox(height: 10.h),
          ScheduleItem(
              icon: ThemeImagePath(
                fileName: "console_schedule_item_002.png",
                imgWidget: 44.r,
                imgHeight: 44.r,
              ),
              title: "console_schedule_two".tr,
              subWidget: Text(
                '${ScheduleStore.to.schedulePlanList.length}',
                style: StyleConfig.otherStyle(
                    color: ColorConfig.searchTextColor,
                    fontSize: 24,
                    fontWeight: FontWeight.w900),
              ),
              onClick: () => controller.toSchedulePlanPage()),
          SizedBox(height: 10.h),
          ScheduleItem(
              icon: ThemeImagePath(
                fileName: "console_schedule_item_003.png",
                imgWidget: 44.r,
                imgHeight: 44.r,
              ),
              title: "console_schedule_three".tr,
              // subWidget: Text(
              //   '3',
              //   style: StyleConfig.otherStyle(
              //       color: ColorConfig.searchTextColor,
              //       fontSize: 24,
              //       fontWeight: FontWeight.w900),
              // ),
              onClick: () => controller.toScheduleALLPage()),
          const Expanded(child: SizedBox()),
          Images(
            path: R.console_schedule_item_image_png,
            width: 224.w,
            height: 155.h,
            boxFit: BoxFit.fill,
          ),
          Container(
            margin: EdgeInsets.only(bottom: 62.h),
            width: 283.w,
            height: 48.h,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24.r),
                color: const Color.fromRGBO(40, 39, 46, 1)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ThemeImagePath(
                  fileName: 'console_schedule_item_add.png',
                  imgWidget: 20.r,
                  imgHeight: 20.r,
                ),
                SizedBox(width: 7.w),
                ThemeText(
                  dataStr: "console_schedule_btn".tr,
                  keyName: 'textColor',
                  fontWeight: FontWeight.w800,
                )
              ],
            ),
          ).inkWell(() => controller.toAddSchedulePage())
        ],
      ),
    );
  }

  @override
  Widget buildContent() {
    return GetBuilder<ConsoleScheduleController>(
      init: ConsoleScheduleController(),
      id: "console_schedule",
      builder: (_) {
        return _buildView();
      },
    );
  }
}
