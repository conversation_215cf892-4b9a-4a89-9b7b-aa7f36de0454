import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/request_body.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/ai_chat/ai_chat_logic.dart';
import 'package:getx_xiaopa/pages/application/application_logic.dart';
import 'package:getx_xiaopa/pages/console/console_schedule/index.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_clear/controller.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_compiled/controller.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_language/controller.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_role/index.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_setting_logic.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_sound/index.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_update/console_update_logic.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_update/widgets/device_update_dialog.dart';
import 'package:getx_xiaopa/pages/console/model/device_items_result.dart';
import 'package:getx_xiaopa/pages/console/widget/floating_text_overlay.dart';
import 'package:getx_xiaopa/pages/console/widget/one_widget.dart';
import 'package:getx_xiaopa/pages/console/widget/select_bottom_widget.dart';
import 'package:getx_xiaopa/pages/diary/diary_logic.dart';
import 'package:getx_xiaopa/pages/mine/mine_device/index.dart';
import 'package:getx_xiaopa/pages/mine/mine_logic.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/widget/widget.dart';
import 'console_state.dart';

class ConsoleLogic extends BaseCommonController {
  @override
  final ConsoleState state = ConsoleState();

  @override
  void initData() async {
    state.deviceItems = UserStore.to.deviceList;

    netState = NetState.dataSuccessState;

    if (state.deviceItems.isNotEmpty) {
      /// 为了保证使用socket的数据，重置
      state.socketModel = UserStore.to.deviceList[UserStore.to.selectDevice];

      ///开启socket连接
      _socketConnect(state.socketModel.mac!);

      state.isBind = true;
      _setValue();

      _getTaskTarget();
    } else {
      state.isBind = false;
    }

    ///请求通知权限
    // PermissionUtil.notification(Get.context!, action: () {});

    update(["console_view"]);

    state.sController.addListener(() {
      ///监听滚动位置设置导航栏颜色
      double opacity;
      opacity = state.sController.offset / 100;

      if (opacity > 0.1) opacity = 0.9;

      state.navColor = state.sController.offset > 5.h
          ? Get.find<ThemeColorController>().gNavColor
          : Colors.transparent;
      update(["console_view"]);
    });

    ///添加订阅
    bus.on("refreshDeviceItems", (data) async {
      state.deviceItems = data;

      if (state.deviceItems.isNotEmpty) {
        ///返回前已经断开，所以不管如何都开启
        _socketConnect(UserStore.to.deviceList[UserStore.to.selectDevice].mac!);

        if (state.socketModel.mac ==
            UserStore.to.deviceList[UserStore.to.selectDevice].mac) {
          logD("重复的直接return");
          return;
        }

        state.cIndex = UserStore.to.selectDevice;

        state.socketModel = UserStore.to.deviceList[UserStore.to.selectDevice];

        Get.find<ThemeColorController>()
            .switchTheme(state.socketModel.theme ?? 1);
        Get.find<ThemeImageController>()
            .switchTheme(state.socketModel.theme ?? 1);

        ///绑定新的设备更新皮肤数据
        await UserStore.to.getUnlockSkinList();

        ///绑定新的更新心情的ui
        Get.find<DiaryLogic>().updateStatus();

        ///绑定新的更新日程
        ScheduleStore.to.init();

        ///更新我的界面的陪伴时间
        Get.find<MineLogic>().updateLicenseExpirDate();

        state.isBind = true;
        _setValue();

        _getTaskTarget();

        /// length=1代表新绑定的，不需要执行切换数字人操作
        if (state.deviceItems.length >= 2) {
          //切换数字人数据
          if (CommonStore.to.showAichat == 1) {
            Get.find<AiChatLogic>().changeBrain();
          }
        }
      } else {
        try {
          await SocketUtil.getInstance()?.disconnect();
        } catch (e) {
          logD("关闭socket出错：$e");
        }
        state.socketModel = DeviceItemsModel();
        state.isBind = false;
        update(["console_view"]);
      }

      Get.find<ApplicationLogic>().updateBottomNavigationBarItemList();

      ///更新我的界面的陪伴时间
      Get.find<MineLogic>().updateLicenseExpirDate();

      if ((CommonStore.to.brainDetail.id ?? '').isNotEmpty) {
        if (CommonStore.to.showAichat == 1) {
          Get.find<AiChatLogic>().loadBrainData();
        }
      }
    });

    ///重要公告弹窗
    if (ConfigStore.to.isToask) {
      if (CommonStore.to.announceContent.isEmpty) return;
      ConfigStore.to.setIsToask();
      Get.dialog(
        DeviceUpdateDialog(
          upDateTitle: CommonStore.to.announceTitle,
          upDateContent: CommonStore.to.announceContent,
          onTap: () {
            Get.back();
          },
        ),
      );
    }
  }

  _socketConnect(String mac) async {
    Future.delayed(const Duration(seconds: 1), () {
      SocketUtil.getInstance(
          serverUrl: 'token=${UserStore.to.authorization}&mac=$mac');
      bus.off("socket_received");
      bus.on("socket_received", _socketMessage);
    });
  }

  _socketMessage(dynamic message) {
    state.socketModel = DeviceItemsModel.fromJson(message);
    _setValue();
  }

  ///给外部调用
  onRefreshData({bool isAnimation = false}) {
    if (isAnimation) {
      state.isRefresh = true;
      update(["console_view"]);
      Future.delayed(const Duration(milliseconds: 500), () {
        _onRefreshData(animation: isAnimation);
      });
    } else {
      _onRefreshData();
    }
  }

  _onRefreshData({bool animation = false}) async {
    await UserStore.to.deviceItems(isChangeTheme: true);
    if (animation) {
      state.isRefresh = false;
      update(["console_view"]);
    }
    state.deviceItems = UserStore.to.deviceList;
    if (state.deviceItems.isNotEmpty) {
      state.socketModel = state.deviceItems[state.cIndex];
      _setValue();
    }
  }

  ///震动
  vibratianAction() async {
    _addFloatingText();
    HapticFeedback.lightImpact();
    await state.soundEffectPlayer.play('assets/audio/btn_music.mp3');
  }

  ///+1往上飘
  _addFloatingText() {
    int a = Random().nextInt(state.shortTextList.length);
    String text = state.shortTextList[a];
    state.floatingTextCount++;
    if (state.floatingTextCount == 10) {
      int b = Random().nextInt(state.longTextList.length);
      text = state.longTextList[b];
      state.floatingTextCount = 0;
    }
    FloatingTextOverlay.of(state.floatingTextKey.currentContext!)
        .show(text, fromKey: state.floatingTextKey);
  }

  @override
  void onHidden() {}

  toPairingPage() async {
    Get.dialog(OneWidget(sAction: () {
      Get.toNamed(AppRoutes.CONSOLE_PAIRING)?.then((value) {
        if (value == "success") {
          EventBus().emit("refreshDeviceItems", UserStore.to.deviceList);
        }
      });
    }));
  }

  toSettingPage() {
    Get.toNamed(AppRoutes.CONSOLE_SETTING)?.then((value) {});
  }

  toTaskPage() {
    Get.toNamed(AppRoutes.TASK, arguments: state.socketModel.id)?.then((value) {
      state.isLoadSkin = false;
      update(["console_view"]);
      _getTaskTarget();
    });
  }

  toSchedulePage() {
    Get.toNamed(AppRoutes.PERIOD);
    // Get.toNamed(AppRoutes.CONSOLE_SCHEDULE)?.then((value) {
    //   update(["console_view"]);
    // });
  }

  /// 修改畅言模式
  interruptMode(flag) {
    if (state.isOnline != 1) {
      CToast.showToast("console_offline_tips".tr);
      return;
    }
    Get.dialog(
      CommonDialog(
          mContent: "console_reset_tips".tr,
          confirmAction: () {
            state.isInterrupt = flag;
            int interruptMode = flag ? 0 : 1;
            sendDeviceConfigAction(
                type: 1, command: "interrupt_mode", value: interruptMode);
            update(["console_view"]);
          }),
    );
  }

  /// 修改静音模式
  silenceMode(flag) {
    if (state.isOnline == 2) {
      CToast.showToast("console_offline_tips".tr);
      return;
    }
    state.isMicrophone = flag;
    int isMicrophone = flag ? 1 : 2;
    sendDeviceConfigAction(
        type: 6, command: "is_microphone", value: isMicrophone);
    update(["console_view"]);
  }

  volumeFocused(value) {
    state.isChangeVolume = true;
  }

  volumeChange(value) {
    state.volumeValue = value;
    update(["console_view"]);
  }

  /// 修改音量
  volumeAction(flag) {
    state.isChangeVolume = false;
    if (state.isOnline != 1) {
      CToast.showToast("console_offline_tips".tr);
      return;
    }
    state.volumeValue = flag;
    int volume = (state.volumeValue * 100).toInt();
    sendDeviceConfigAction(type: 4, command: "volume", value: volume);
  }

  _setValue() {
    state.powerValue = (state.socketModel.power ?? 0) / 100;

    if (!state.isChangeVolume) {
      state.volumeValue = (state.socketModel.volume ?? 0) / 100;
    }

    state.sliderController.value = state.volumeValue;

    state.isInterrupt = state.socketModel.interruptMode == 0 ? true : false;

    state.isMicrophone = state.socketModel.isMicrophone == 1 ? true : false;

    if (state.socketModel.isOnline == 2) {
      state.isOnline = 2;
      state.onlineTips = "console_offline".tr;
      state.onlineImage = R.console_bind_wifi_off_png;
    } else if (state.socketModel.isOnline == 1 &&
        state.socketModel.isMicrophone == 1) {
      state.isOnline = 3;
      state.onlineTips = "console_jingyin".tr;
      state.onlineImage = Get.find<ThemeImageController>().onlineJingYin;
    } else if (state.socketModel.isOnline == 1 &&
        state.socketModel.isSleep == 1) {
      state.isOnline = 4;
      state.onlineTips = "console_xiumian".tr;
      state.onlineImage = Get.find<ThemeImageController>().onlineXiuMian;
    } else if (state.socketModel.isOnline == 1 &&
        state.socketModel.isSleep == 2 &&
        state.socketModel.isMicrophone == 2) {
      state.isOnline = 1;
      state.onlineTips = "console_online".tr;
      state.onlineImage = R.console_bind_wifi_on_png;
    } else {
      state.isOnline = 2;
      state.onlineTips = "console_offline".tr;
      state.onlineImage = R.console_bind_wifi_off_png;
    }

    if (Get.isRegistered<MineLogic>()) {
      Get.find<MineLogic>().updateStatus(state.socketModel);
    } else {
      Get.put(MineLogic());
      Get.find<MineLogic>().updateStatus(state.socketModel);
    }

    if (Get.isRegistered<ConsoleUpdateLogic>()) {
      Get.find<ConsoleUpdateLogic>().updateStatus(state.socketModel);
    } else {
      Get.put(ConsoleUpdateLogic());
      Get.find<ConsoleUpdateLogic>().updateStatus(state.socketModel);
    }

    if (Get.isRegistered<ConsoleSettingLogic>()) {
      Get.find<ConsoleSettingLogic>().updateStatus(state.socketModel);
    } else {
      Get.put(ConsoleSettingLogic());
      Get.find<ConsoleSettingLogic>().updateStatus(state.socketModel);
    }

    if (Get.isRegistered<ConsoleCompiledController>()) {
      Get.find<ConsoleCompiledController>().updateStatus(state.socketModel);
    } else {
      Get.put(ConsoleCompiledController());
      Get.find<ConsoleCompiledController>().updateStatus(state.socketModel);
    }

    if (Get.isRegistered<ConsoleSoundController>()) {
      Get.find<ConsoleSoundController>().updateStatus(state.socketModel);
    } else {
      Get.put(ConsoleSoundController());
      Get.find<ConsoleSoundController>().updateStatus(state.socketModel);
    }

    if (Get.isRegistered<ConsoleLanguageController>()) {
      Get.find<ConsoleLanguageController>().updateStatus(state.socketModel);
    } else {
      Get.put(ConsoleLanguageController());
      Get.find<ConsoleLanguageController>().updateStatus(state.socketModel);
    }

    if (Get.isRegistered<ConsoleClearController>()) {
      Get.find<ConsoleClearController>().updateStatus(state.socketModel);
    } else {
      Get.put(ConsoleClearController());
      Get.find<ConsoleClearController>().updateStatus(state.socketModel);
    }

    if (Get.isRegistered<ConsoleRoleController>()) {
      Get.find<ConsoleRoleController>().updateStatus(state.socketModel);
    } else {
      Get.put(ConsoleRoleController());
      Get.find<ConsoleRoleController>().updateStatus(state.socketModel);
    }

    if (Get.isRegistered<MineDeviceController>()) {
      Get.find<MineDeviceController>().updateStatus(state.socketModel);
    } else {
      Get.put(MineDeviceController());
      Get.find<MineDeviceController>().updateStatus(state.socketModel);
    }

    update(["console_view"]);
  }

  /// 切换设备
  selectDeviceAction() {
    state.cIndex = UserStore.to.selectDevice;
    Get.bottomSheet(
      enableDrag: false,
      SelectBottomWidget(
        cIndex: state.cIndex,
        deviceItems: state.deviceItems,
        onTap: (index) {
          changeDevice(index);
          Get.back();
        },
      ),
    );
  }

  /// 切换设备
  changeDevice(index) async {
    if (state.socketModel.mac == UserStore.to.deviceList[index].mac) {
      logD("重复的直接return");
      return;
    }

    state.cIndex = index;

    UserStore.to.setSelectDevice(index);

    ///切换设备先赋值，防止socket没有数据
    state.socketModel = UserStore.to.deviceList[UserStore.to.selectDevice];

    await SocketUtil.getInstance()?.disconnect();
    _socketConnect(state.socketModel.mac!);

    ///选择设备更新皮肤数据
    await UserStore.to.getUnlockSkinList();

    ///切换设备根据主题颜色改变
    Get.find<ThemeColorController>().switchTheme(state.socketModel.theme ?? 1);
    Get.find<ThemeImageController>().switchTheme(state.socketModel.theme ?? 1);

    ///更新心情界面的数据
    Get.find<DiaryLogic>().updateStatus();

    ///更新日程的数据
    ScheduleStore.to.init();

    _setValue();

    _getTaskTarget();

    ///切换数字人数据
    if (CommonStore.to.showAichat == 1) {
      Get.find<AiChatLogic>().changeBrain();
    }
  }

  ///========================网络请求===============================
  /// 发送修改设备配置
  /// type 1：重启 2：查询电量音量 3：升级 4：设置音量 5：查询版本号 6：设置麦克风 7：设置提醒
  sendDeviceConfigAction(
      {int? type, String command = "", dynamic value = ""}) async {
    var map = {
      "device_id": state.deviceItems[state.cIndex].id,
      "command": command,
      "value": value,
      "type": type
    };
    Result result = await http.setUpDevice(map);
    if (result.code != 0) {
      CToast.showToast(result.msg!);
    }
  }

  ///任务目标
  _getTaskTarget() async {
    var map = RequestBody(filters: [
      Filter(expr: "=", name: "device_id", value: state.socketModel.id)
    ]).toJson();
    Result result = await http.taskTargetItems(map);
    if (result.code == 0) {
      if (result.data.data.isNotEmpty) {
        state.maxCupSchedule = result.data.data[0].theNum ?? 8;
        _getTaskItems();
      }
    }
    state.isLoadSkin = true;
    update(["console_view"]);
  }

  ///任务列表
  _getTaskItems() async {
    var map = RequestBody(pagination: false, filters: [
      Filter(expr: "=", name: "device_id", value: state.socketModel.id),
      Filter(expr: ">=", name: "created_at", value: getNowDate())
    ]).toJson();
    Result result = await http.taskItems(map);
    if (result.code == 0) {
      state.cupSliderSchedule = result.data.data.length;
      if (state.cupSliderSchedule >= state.maxCupSchedule) {
        state.isDrinkSuccess = true;
      }
    }
    update(["console_view"]);
  }

  ///获取当前日期
  ///[isDay] 是否返回当天多少号
  String getNowDate({bool isDay = false}) {
    DateTime temp = DateTime.now();
    String dateStr = "";
    if (isDay) {
      dateStr = "${temp.day}";
    } else {
      dateStr = "${temp.year}-${temp.month}-${temp.day} 00:00:00";
    }
    return dateStr;
  }

  deviceDetail() async {
    var map = {"id": state.socketModel.id};
    Result result = await http.deviceDetail(map);
    if (result.code == 0) {
      state.socketModel = result.data;
      _setValue();
    }
  }

  @override
  void onClose() {
    super.onClose();
    SocketUtil.getInstance()?.disconnect();
    state.soundEffectPlayer.dispose();
    state.sController.dispose();
    bus.off("socket_received");
    bus.off("refreshDeviceItems");
  }
}
