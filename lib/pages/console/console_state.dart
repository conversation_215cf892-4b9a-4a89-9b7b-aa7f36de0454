import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/pages/console/model/device_items_result.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/widget/slider/custom_slider_controller.dart';

class ConsoleState {
  /// 是否绑定了小耙  默认false
  bool isBind = false;

  ///是否充电状态 默认false
  bool isCharge = false;

  ///电量
  double powerValue = 0.5;

  ///是否在线 1=在线 2=离线 3=静音 4=休眠
  int isOnline = 2;
  String onlineTips = "console_off".tr;
  String onlineImage = R.console_bind_wifi_off_png;

  ///音量
  double volumeValue = 0.5;

  ///是否打断模式 1=关闭打断
  bool isInterrupt = false;

  ///是否休眠 1=开启休眠
  bool isMicrophone = false;

  ///默认选择第一个设备
  int cIndex = 0;
  List<DeviceItemsModel> deviceItems = [];

  DeviceItemsModel socketModel = DeviceItemsModel();

  Color navColor = Colors.transparent;
  final ScrollController sController = ScrollController();

  /// 是否正在刷新
  bool isRefresh = false;

  final CustomSliderController sliderController = CustomSliderController(0);

  final SoundEffectPlayer soundEffectPlayer = SoundEffectPlayer(volume: 0.5);

  ///是否正在修改音量
  bool isChangeVolume = false;

  GlobalKey floatingTextKey = GlobalKey();

  int floatingTextCount = 0;

  ///刷新喝水皮肤
  bool isLoadSkin = false;

  ///敲打短文案
  List<String> shortTextList = [
    'console_short_one'.tr,
    'console_short_two'.tr,
    'console_short_three'.tr,
    'console_short_four'.tr,
    'console_short_five'.tr,
    'console_short_six'.tr,
    'console_short_seven'.tr,
    'console_short_eight'.tr,
    'console_short_nine'.tr,
    'console_short_ten'.tr
  ];

  ///敲打长文案
  List<String> longTextList = [
    'console_long_one'.tr,
    'console_long_two'.tr,
    'console_long_three'.tr,
    'console_long_four'.tr,
    'console_long_five'.tr,
    'console_long_six'.tr,
    'console_long_seven'.tr,
    'console_long_eight'.tr,
    'console_long_nine'.tr,
    'console_long_ten'.tr,
  ];

  /// 当前喝水进度（进度条的进度）
  int cupSliderSchedule = 0;

  /// 每天目标
  int maxCupSchedule = 8;

  bool isDrinkSuccess = false;

  ConsoleState() {
    ///Initialize variables
  }
}
