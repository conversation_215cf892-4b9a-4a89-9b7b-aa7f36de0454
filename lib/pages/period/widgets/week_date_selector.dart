import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/pages/period/index.dart';

import 'package:getx_xiaopa/values/values.dart';

class WeekDateSelector extends StatefulWidget {
  final PeriodCycleInfoModel model;
  const WeekDateSelector({super.key, required this.model});

  @override
  State<WeekDateSelector> createState() => _WeekDateSelectorState();
}

class _WeekDateSelectorState extends State<WeekDateSelector> {
  final DateTime today = DateTime.now();
  List<DateTime> days = [];

  Color _selectedColor = Colors.transparent;

  @override
  void initState() {
    super.initState();
    days = List.generate(
      7,
      (i) => DateTime(today.year, today.month, today.day)
          .add(Duration(days: i - 3)),
    );
    _selectedColor = PeriodUtils.getPersionMainColor(widget.model.phase ?? 1);
  }

  @override
  void didUpdateWidget(WeekDateSelector oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当model数据更新时，重新计算颜色
    if (oldWidget.model.phase != widget.model.phase) {
      _selectedColor = PeriodUtils.getPersionMainColor(widget.model.phase ?? 1);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: days.map((date) {
        final bool isToday = date.year == today.year &&
            date.month == today.month &&
            date.day == today.day;
        PeriodType pType =
            PeriodUtils.getPersionColor(model: widget.model, cTime: date);

        return Container(
          width: isToday ? 43.w : 36.w,
          height: isToday ? 72.h : 60.h,
          padding: EdgeInsets.all(3.r),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(18.r),
              color: Colors.white,
              boxShadow: const [
                BoxShadow(
                  color: Color.fromRGBO(255, 232, 174, 0.27),
                  // 阴影颜色和透明度
                  spreadRadius: 0,
                  // 阴影扩散范围
                  blurRadius: 18,
                  // 阴影模糊程度
                  offset: Offset(0, 2),
                  // 阴影偏移量（水平，垂直）
                )
              ]),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              isToday
                  ? _BubbleDay(
                      day: date.day,
                      diameter: 35,
                      color: _selectedColor,
                      textColor: Colors.white,
                    )
                  : _CircleDay(
                      day: date.day,
                      diameter: 30,
                      color: pType.isPeriod ? pType.color : Colors.transparent,
                      textColor: pType.isPeriod ? Colors.white : pType.color,
                    ),
              SizedBox(height: isToday ? 0 : 4.h),
              Text(
                _weekdayCN(date.weekday),
                style: StyleConfig.otherStyle(
                    color: isToday
                        ? const Color.fromRGBO(255, 80, 110, 1)
                        : ColorConfig.shopDetailTextColor,
                    fontSize: isToday ? 12 : 10,
                    fontWeight: FontWeight.w600,
                    height: 1),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  static String _weekdayCN(int w) {
    switch (w) {
      case 1:
        return 'diary_week_one'.tr;
      case 2:
        return 'diary_week_two'.tr;
      case 3:
        return 'diary_week_three'.tr;
      case 4:
        return 'diary_week_four'.tr;
      case 5:
        return 'diary_week_five'.tr;
      case 6:
        return 'diary_week_six'.tr;
      case 7:
        return 'diary_week_seven'.tr;
      default:
        return '';
    }
  }
}

/// 普通圆
class _CircleDay extends StatelessWidget {
  final int day;
  final double diameter;
  final Color color;
  final Color textColor;

  const _CircleDay({
    required this.day,
    required this.diameter,
    required this.color,
    required this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: diameter,
      height: diameter,
      child: DecoratedBox(
        decoration: BoxDecoration(shape: BoxShape.circle, color: color),
        child: Center(
          child: Text(
            '$day',
            style: StyleConfig.otherStyle(
                color: textColor, fontSize: 14, fontWeight: FontWeight.w600),
          ),
        ),
      ),
    );
  }
}

/// 中间的“圆 + 圆角箭头”小气泡
class _BubbleDay extends StatelessWidget {
  final int day;
  final double diameter; // 圆部分直径

  final Color color;
  final Color textColor;

  const _BubbleDay({
    required this.day,
    required this.diameter,
    required this.color,
    required this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    final double totalH = diameter + 12;
    return SizedBox(
      width: diameter,
      height: totalH,
      child: CustomPaint(
        painter: _BubblePainter(color),
        child: Center(
          // 数字在圆心（不是整体高度的中心），所以用 Padding 微调到圆心
          child: Padding(
            padding: const EdgeInsets.only(bottom: 12), // 把文字抬到圆里
            child: Text(
              '$day',
              style: StyleConfig.otherStyle(
                  color: textColor, fontSize: 14, fontWeight: FontWeight.w600),
            ),
          ),
        ),
      ),
    );
  }
}

class _BubblePainter extends CustomPainter {
  final Color color;

  _BubblePainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill
      ..isAntiAlias = true
      ..strokeJoin = StrokeJoin.round
      ..strokeCap = StrokeCap.round;

    // 先画一个稍大的背景来覆盖可能的缝隙
    final Paint bgPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;

    double r = size.width / 2; // 圆的半径
    double arrowHeight = size.height - size.width; // 箭头部分的高度
    double arrowWidth = 12; // 箭头底部宽度
    Offset center = Offset(r, r);

    // 先画背景圆，稍微大一点
    canvas.drawCircle(center, r + 1, bgPaint);

    final Path path = Path();

    // 添加圆
    path.addOval(Rect.fromCircle(center: center, radius: r));

    // 从圆的底部中心点开始画箭头
    path.moveTo(center.dx - arrowWidth / 2, size.width);

    // 左下角圆弧
    path.quadraticBezierTo(
      center.dx,
      size.width + arrowHeight,
      center.dx + arrowWidth / 2,
      size.width,
    );

    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
