import 'package:flutter/material.dart';
import 'dart:math' as math;

import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/pages/period/index.dart';
import 'package:getx_xiaopa/r.dart';

class PeriodCycle extends StatefulWidget {
  final PeriodCycleInfoModel model;

  const PeriodCycle({
    super.key,
    required this.model,
  });

  @override
  State<PeriodCycle> createState() => _PeriodCycleState();
}

class _PeriodCycleState extends State<PeriodCycle> {
  // 定义各个时期的颜色和天数
  List _phases = [];

  int _dayCount = 0;
  int _currentDay = 0;
  int _cycleDays = 0;
  String _currentImage = R.period_circle_image_01_png;

  @override
  void initState() {
    super.initState();
    _initData();
  }

  _initData() {
    _phases = [
      {
        'days': widget.model.periodDuration,
        'color': const Color.fromRGBO(254, 139, 172, 1), // 粉色
        'inactiveColor': const Color.fromRGBO(255, 220, 224, 1), // 浅粉色
        'image': R.period_circle_image_01_png, // 经期图片
      },
      {
        'days': widget.model.follicularDuration,
        'color': const Color.fromRGBO(155, 231, 119, 1), // 绿色
        'inactiveColor': const Color.fromRGBO(201, 239, 180, 1), // 浅绿色
        'image': R.period_circle_image_02_png, // 经期图片
      },
      {
        'days': widget.model.ovulationDuration,
        'color': const Color.fromRGBO(172, 127, 255, 1), // 紫色
        'inactiveColor': const Color.fromRGBO(195, 189, 252, 1), // 浅紫色
        'image': R.period_circle_image_03_png, // 经期图片
      },
      {
        'days': widget.model.lutealDuration,
        'color': const Color.fromRGBO(255, 211, 9, 1), // 黄色
        'inactiveColor': const Color.fromRGBO(255, 232, 174, 1), // 浅黄色
        'image': R.period_circle_image_04_png,
      },
    ];

    _currentDay = _beforeDay(widget.model) + widget.model.dayInPhase!;
    _cycleDays = widget.model.cycleDuration ?? 30;
    for (int i = 0; i < _phases.length; i++) {
      final phaseDays = _phases[i]['days'] as int;
      if (_currentDay <= _dayCount + phaseDays) {
        _currentImage = _phases[i]['image'] as String;
        break;
      }
      _dayCount += phaseDays;
    }
  }

  @override
  void didUpdateWidget(PeriodCycle oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当model数据更新时，重新计算颜色
    if (oldWidget.model != widget.model) {
      _initData();
    }
  }

  ///计算处于当前时期前面是否还有其他时期
  int _beforeDay(PeriodCycleInfoModel model) {
    switch (model.phase) {
      case 1:
        return 0;
      case 2:
        return model.periodDuration!;
      case 3:
        return (model.periodDuration! + model.follicularDuration!);
      case 4:
        return (model.periodDuration! +
            model.follicularDuration! +
            model.ovulationDuration!);
      default:
        return 0;
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 235.r,
      height: 235.r,
      child: CustomPaint(
        painter: _PeriodCyclePainter(
            currentDay: _currentDay, cycleDays: _cycleDays, phases: _phases),
        child: Center(
          child: Container(
            width: 150.r,
            height: 150.r,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white.withValues(alpha: 0.5),
            ),
            child: Images(path: _currentImage),
          ),
        ),
      ),
    );
  }
}

class _PeriodCyclePainter extends CustomPainter {
  final int currentDay;
  final int cycleDays;
  final List phases;

  _PeriodCyclePainter({
    required this.currentDay,
    required this.cycleDays,
    required this.phases,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.45;
    const strokeWidth = 30.0;
    const gapAngle = 0.4; // 每个阶段之间的间隔角度

    double startAngle = -math.pi / 2; // 从顶部开始
    final totalGaps = phases.length * gapAngle;
    final availableAngle = 2 * math.pi - totalGaps;

    // 计算当前处于哪个阶段和阶段内的第几天
    int dayCount = 0;
    int currentPhaseIndex = 0;
    int dayInPhase = 0;

    for (int i = 0; i < phases.length; i++) {
      final phaseDays = phases[i]['days'] as int;
      if (currentDay <= dayCount + phaseDays) {
        currentPhaseIndex = i;
        dayInPhase = currentDay - dayCount;
        break;
      }
      dayCount += phaseDays;
    }

    // 绘制各个时期的弧形
    for (int i = 0; i < phases.length; i++) {
      final phase = phases[i];
      final days = phase['days'] as int;
      final isCurrentPhase = i == currentPhaseIndex;
      final color = isCurrentPhase
          ? phase['color'] as Color
          : phase['inactiveColor'] as Color;
      final sweepAngle = (days / cycleDays) * availableAngle;

      if (i == 2) {
        // 排卵期 (索引2)
        // 绘制排卵期的特殊形状 - 圆点
        _drawOvulationDot(
            canvas, center, radius, startAngle + sweepAngle / 2, color);
      } else {
        // 绘制普通弧形
        final paint = Paint()
          ..color = color
          ..strokeWidth = strokeWidth
          ..style = PaintingStyle.stroke
          ..strokeCap = StrokeCap.round;

        canvas.drawArc(
          Rect.fromCircle(center: center, radius: radius),
          startAngle,
          sweepAngle,
          false,
          paint,
        );
      }

      // 只在当前阶段显示点
      if (isCurrentPhase && i != 2) {
        // 排卵期不显示额外的点
        _drawDots(canvas, center, radius, startAngle, sweepAngle,
            Colors.white.withValues(alpha: 0.8), days, dayInPhase);
      }

      startAngle += sweepAngle + gapAngle; // 加上间隔
    }

    // 绘制当前日期的三角形指示器
    _drawTriangleIndicator(
        canvas, center, radius, currentDay, cycleDays, phases);
  }

  void _drawDots(Canvas canvas, Offset center, double radius, double startAngle,
      double sweepAngle, Color color, int totalDays, int currentDayInPhase) {
    final dotPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final highlightPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    // 绘制该阶段所有天数的点
    for (int i = 0; i < totalDays; i++) {
      final angle = startAngle + (sweepAngle * (i + 0.5) / totalDays);
      final dotCenter = Offset(
        center.dx + radius * math.cos(angle),
        center.dy + radius * math.sin(angle),
      );

      // 绘制普通点
      canvas.drawCircle(dotCenter, 4, dotPaint);

      // 如果是当前天数，绘制圆圈高亮
      if (i + 1 == currentDayInPhase) {
        canvas.drawCircle(dotCenter, 8, highlightPaint);
      }
    }
  }

  void _drawOvulationDot(
      Canvas canvas, Offset center, double radius, double angle, Color color) {
    final flowerCenter = Offset(
      center.dx + radius * math.cos(angle),
      center.dy + radius * math.sin(angle),
    );

    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // 绘制花朵形状 - 5个花瓣
    const petalCount = 5;
    const petalRadius = 30;
    const centerRadius = 4;

    // 绘制花瓣
    for (int i = 0; i < petalCount; i++) {
      final petalAngle = (2 * math.pi / petalCount) * i;
      final petalCenter = Offset(
        flowerCenter.dx + centerRadius * math.cos(petalAngle),
        flowerCenter.dy + centerRadius * math.sin(petalAngle),
      );

      // 绘制椭圆形花瓣
      canvas.save();
      canvas.translate(petalCenter.dx, petalCenter.dy);
      canvas.rotate(petalAngle);

      final petalPath = Path();
      petalPath.addOval(Rect.fromCenter(
        center: Offset.zero,
        width: petalRadius * 0.6,
        height: petalRadius * 1.2,
      ));

      canvas.drawPath(petalPath, paint);
      canvas.restore();
    }
  }

  void _drawTriangleIndicator(Canvas canvas, Offset center, double radius,
      int currentDay, int cycleDays, List phases) {
    // 计算当前日期对应的角度，需要考虑间隔
    const totalGaps = 4 * 0.3; // phases.length * gapAngle
    const availableAngle = 2 * math.pi - totalGaps;

    // 计算当前处于哪个阶段
    int dayCount = 0;
    double currentAngle = -math.pi / 2;

    for (int i = 0; i < phases.length; i++) {
      final phaseDays = phases[i]['days'] as int;
      if (currentDay <= dayCount + phaseDays) {
        // 在当前阶段内
        final dayInPhase = currentDay - dayCount;
        final phaseAngle = (phaseDays / cycleDays) * availableAngle;
        final angleInPhase = (dayInPhase - 0.5) / phaseDays * phaseAngle;
        currentAngle += angleInPhase;
        break;
      }
      dayCount += phaseDays;
      final phaseAngle = (phaseDays / cycleDays) * availableAngle;
      currentAngle += phaseAngle + 0.3; // 加上间隔
    }

    // 三角形指示器的位置（从中心圆形边缘开始）
    const innerRadius = 80.0; // 中心圆形的半径
    final triangleCenter = Offset(
      center.dx + innerRadius * math.cos(currentAngle),
      center.dy + innerRadius * math.sin(currentAngle),
    );

    final paint = Paint()
      ..color = const Color(0xFFFF6B9D)
      ..style = PaintingStyle.fill;

    // 绘制三角形
    final path = Path();
    const triangleSize = 10.0;

    // 计算三角形的三个顶点，指向外面
    final tip = Offset(
      triangleCenter.dx + triangleSize * math.cos(currentAngle),
      triangleCenter.dy + triangleSize * math.sin(currentAngle),
    );

    final left = Offset(
      triangleCenter.dx +
          triangleSize * math.cos(currentAngle - math.pi * 2 / 3),
      triangleCenter.dy +
          triangleSize * math.sin(currentAngle - math.pi * 2 / 3),
    );

    final right = Offset(
      triangleCenter.dx +
          triangleSize * math.cos(currentAngle + math.pi * 2 / 3),
      triangleCenter.dy +
          triangleSize * math.sin(currentAngle + math.pi * 2 / 3),
    );

    path.moveTo(tip.dx, tip.dy);
    path.lineTo(left.dx, left.dy);
    path.lineTo(right.dx, right.dy);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
