import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/pages/period/index.dart';
import 'package:getx_xiaopa/pages/period/period_analysis/index.dart';
import 'package:getx_xiaopa/utils/utils.dart';

class PeriodDayController extends BaseListController {
  PeriodDayController();

  List<PeriodItemModel> periodModelItems = [];
  List<PeriodItemModel> tempList = [];
  List<OneType> ontTypeList = [];
  final int _pageSize = 20;

  @override
  void initData() {
    _periodData();
    netState = NetState.dataSuccessState;
    update(["period_day"]);
  }

  @override
  void onHidden() {}

  @override
  refreshData() {
    super.refreshData();
    page = 1;
    _periodData();
  }

  @override
  loadMore() {
    super.loadMore();
    if (tempList.isEmpty) {
      refreshController.loadNoData();
      return;
    }
    page++;
    _periodData();
  }

  _periodData() async {
    if (page == 1) {
      periodModelItems.clear();
      ontTypeList.clear();
      tempList.clear();
      update(["period_day"]);
      tempList = await PeriodStore.to
          .periodItem(isAll: true, page: page, pageSize: _pageSize);
      refreshController.refreshCompleted();
      refreshController.loadComplete();
    } else {
      tempList = await PeriodStore.to
          .periodItem(isAll: true, page: page, pageSize: _pageSize);
      if (tempList.isEmpty) {
        refreshController.loadNoData();
        Future.delayed(const Duration(seconds: 1), () {
          refreshController.loadComplete();
        });
        return;
      }
      refreshController.loadComplete();
    }

    // 创建新列表避免并发修改异常
    final newList = List<PeriodItemModel>.from(periodModelItems);
    newList.addAll(tempList);
    periodModelItems = newList;

    for (var i = 0; i < periodModelItems.length; i++) {
      OneType oneType = OneType();
      PeriodItemModel periodItemModel = periodModelItems[i];

      oneType.date =
          "${TimeUtil.getLocaleTimeWithWeek(TimeUtil.parseIsoDate(periodItemModel.startDate!), isEn: true, isZero: true)}~${TimeUtil.getLocaleTimeWithWeek(TimeUtil.parseIsoDate(periodItemModel.endDate!), isEn: true, isZero: true)}";
      oneType.pValue = periodItemModel.realDuration!.toDouble();
      oneType.beforeTips = 'period_analysis_one_widget_three'
          .trArgs(['${periodItemModel.realDuration}']);
      oneType.pColor = (oneType.pValue > 7 || oneType.pValue < 2)
          ? const Color.fromRGBO(255, 211, 9, 1)
          : const Color.fromRGBO(254, 139, 172, 1);

      ontTypeList.add(oneType);
    }

    if (periodModelItems.length < _pageSize) {
      refreshController.loadNoData();
      Future.delayed(const Duration(seconds: 1), () {
        refreshController.loadComplete();
      });
    }

    update(["period_day"]);
  }
}
