import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base_list_view.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';

import 'index.dart';

class PeriodFluxPage extends BaseListView<PeriodFluxController> {
  PeriodFluxPage({super.key});

  @override
  String? get navTitle => "period_analysis_flux_title".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w600);

  @override
  Color? get navColor => Colors.transparent;

  @override
  double? get leftWidth => 35.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  @override
  Color? get contentColor => const Color.fromRGBO(249, 249, 249, 1);

  // 主视图
  Widget _buildView() {
    return Container(
      margin: EdgeInsets.only(left: 15.w, right: 15.w, top: 10.h, bottom: 26.h),
      width: 345.w,
      height: 677.h,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              spreadRadius: 0,
              blurRadius: 10,
              offset: const Offset(0, 2),
            )
          ]),
      child: createRefresherListView(controller, (_) {
        return ListView.builder(
          itemCount: controller.fourTypeMap.keys.length,
          itemBuilder: (_, index) {
            String dataKey = controller.fourTypeMap.keys.toList()[index];
            return PeriodFluxItem(
              dataKey: dataKey,
              fourType: controller.fourTypeMap[dataKey] ?? [],
            );
          },
        );
      }),
    );
  }

  @override
  Widget buildContent() {
    return GetBuilder<PeriodFluxController>(
      init: PeriodFluxController(),
      id: "period_flux",
      builder: (_) {
        return _buildView();
      },
    );
  }
}
