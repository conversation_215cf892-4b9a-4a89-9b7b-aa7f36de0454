import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/pages/period/period_analysis/index.dart';
import 'package:getx_xiaopa/values/values.dart';

class PeriodColorItem extends StatefulWidget {
  final String dataKey;
  final List<ThreeType> threeType;

  const PeriodColorItem(
      {super.key, required this.threeType, required this.dataKey});

  @override
  State<PeriodColorItem> createState() => _PeriodColorItemState();
}

class _PeriodColorItemState extends State<PeriodColorItem> {
  String _key = '';
  final List<String> _image = [];
  final List<String> _str = [];

  @override
  void initState() {
    super.initState();
    _key = widget.dataKey;

    for (ThreeType threeType in widget.threeType) {
      _str.add(threeType.colorDesc);
      _image.add(threeType.image);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 14.h),
      padding: EdgeInsets.only(left: 10.w, right: 10.w),
      width: 325.w,
      constraints: BoxConstraints(minHeight: 69.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(left: 5.w),
            child: Text(
              _key,
              style: StyleConfig.otherStyle(color: ColorConfig.searchTextColor),
            ),
          ),
          SizedBox(height: 14.h),
          _image.isEmpty
              ? Container(
                  margin: EdgeInsets.only(left: 5.w),
                  child: Text(
                    'period_analysis_color_two'.tr,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.shopDetailTextColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w500),
                  ),
                )
              : Container(
                  margin: EdgeInsets.only(left: 5.w),
                  height: 74.h,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    children: List.generate(_image.length, (index) {
                      return Container(
                        margin: EdgeInsets.only(left: index > 0 ? 20.w : 0),
                        child: Column(
                          children: [
                            Text(
                              'period_analysis_pain_chart_day'
                                  .trArgs(['${index + 1}']),
                              style: StyleConfig.otherStyle(
                                  color: ColorConfig.authenticationTextColor,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500),
                            ),
                            Images(
                              path: _image[index],
                              width: 40.r,
                              height: 40.r,
                            ),
                            Text(
                              _str[index],
                              style: StyleConfig.otherStyle(
                                  color: ColorConfig.shopDetailTextColor,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500),
                            ),
                          ],
                        ),
                      );
                    }),
                  ),
                ),
          Container(
            margin: EdgeInsets.only(top: 16.h),
            width: 325.w,
            height: 1.h,
            color: const Color.fromRGBO(233, 232, 233, 1),
          )
        ],
      ),
    );
  }
}
