import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/pages/period/period_analysis/index.dart';
import 'package:getx_xiaopa/pages/period/period_analysis/period_pain/index.dart';
import 'package:getx_xiaopa/values/values.dart';

class PeriodPainItem extends StatefulWidget {
  final String dataKey;
  final List<FourType> fourType;
  const PeriodPainItem({
    super.key,
    required this.dataKey,
    required this.fourType,
  });

  @override
  State<PeriodPainItem> createState() => _PeriodPainItemState();
}

class _PeriodPainItemState extends State<PeriodPainItem> {
  final List<String> _statusLabels = [
    "period_four_bottom_pain_five".tr,
    "period_four_bottom_pain_four".tr,
    "period_four_bottom_pain_three".tr,
    "period_four_bottom_pain_two".tr,
    "period_four_bottom_pain_one".tr
  ];
  final List<int> _painLevels = []; // 0-4 对应完全不痛到痛到极致
  final List<String> _dayLabels = []; // 第1天、第2天等

  @override
  void initState() {
    super.initState();
    for (FourType type in widget.fourType) {
      _painLevels.add(type.num);
      _dayLabels.add(type.day);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 14.h),
      padding: EdgeInsets.only(left: 10.w, right: 10.w),
      width: 325.w,
      constraints: BoxConstraints(minHeight: 69.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(left: 5.w),
            child: Text(
              widget.dataKey,
              style: StyleConfig.otherStyle(color: ColorConfig.searchTextColor),
            ),
          ),
          SizedBox(height: 12.h),
          _painLevels.isEmpty
              ? Container(
                  margin: EdgeInsets.only(left: 5.w),
                  child: Text(
                    'period_analysis_no_data'.tr,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.shopDetailTextColor, fontSize: 14),
                  ),
                )
              : PainChartWidget(
                  painLevels: _painLevels,
                  dayLabels: _dayLabels,
                  statusLabels: _statusLabels),
          SizedBox(height: 16.h),
          Container(
            width: 325.w,
            height: 1.h,
            color: const Color.fromRGBO(233, 232, 233, 1),
          )
        ],
      ),
    );
  }
}
