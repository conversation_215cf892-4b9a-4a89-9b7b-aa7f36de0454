import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/pages/period/index.dart';
import 'package:getx_xiaopa/pages/period/period_analysis/index.dart';
import 'package:getx_xiaopa/utils/utils.dart';

class PeriodPainController extends BaseListController {
  PeriodPainController();

  List<int> painLevels = [];
  List<String> dayLabels = [];

  List<PeriodItemModel> periodModelItems = [];
  List<PeriodItemModel> tempList = [];
  Map<String, List<FourType>> fourTypeMap = {};
  final int _pageSize = 10;

  @override
  void onReady() {
    super.onReady();
    _periodData();
    netState = NetState.dataSuccessState;
    update(["period_pain"]);
  }

  @override
  void initData() {
    _periodData();
    netState = NetState.dataSuccessState;
    update(["period_pain"]);
  }

  @override
  void onHidden() {}

  @override
  refreshData() {
    super.refreshData();
    page = 1;
    _periodData();
  }

  @override
  loadMore() {
    super.loadMore();
    if (tempList.isEmpty) {
      refreshController.loadNoData();
      return;
    }
    page++;
    _periodData();
  }

  _periodData() async {
    if (page == 1) {
      periodModelItems.clear();
      fourTypeMap.clear();
      tempList.clear();
      update(["period_pain"]);
      tempList = await PeriodStore.to
          .periodItem(isAll: true, page: page, pageSize: _pageSize);
      refreshController.refreshCompleted();
      refreshController.loadComplete();
    } else {
      tempList = await PeriodStore.to
          .periodItem(isAll: true, page: page, pageSize: _pageSize);
      if (tempList.isEmpty) {
        refreshController.loadNoData();
        Future.delayed(const Duration(seconds: 1), () {
          refreshController.loadComplete();
        });
        return;
      }
      refreshController.loadComplete();
    }

    // 创建新列表避免并发修改异常
    final newList = List<PeriodItemModel>.from(periodModelItems);
    newList.addAll(tempList);
    periodModelItems = newList;

    for (PeriodItemModel model in periodModelItems) {
      String sPeriodDate = TimeUtil.getLocaleTimeWithWeek(
          TimeUtil.parseIsoDate(model.startDate!));
      String ePeriodDate =
          TimeUtil.getLocaleTimeWithWeek(TimeUtil.parseIsoDate(model.endDate!));
      String tempKey = '$sPeriodDate-$ePeriodDate';
      if (!(model.isEnd ?? true)) {
        tempKey = '$sPeriodDate-${'period_analysis_color_three'.tr}';
      }

      if (!fourTypeMap.containsKey(tempKey)) {
        fourTypeMap[tempKey] = [];
      }
      if (model.records != null) {
        for (var i = 0; i < (model.realDuration ?? 0); i++) {
          FourType fourType = FourType();
          fourType.day = 'period_analysis_pain_chart_day'.trArgs(['${i + 1}']);
          for (PeriodItemModelRecords record
              in model.records!.whereType<PeriodItemModelRecords>()) {
            if (record.dayNumber == i + 1) {
              fourType.num = int.parse(record.pain ?? '0');
              break;
            } else {
              fourType.num = 0;
            }
          }
          fourTypeMap[tempKey]!.add(fourType);
        }
      }

      if (periodModelItems.length < _pageSize) {
        refreshController.loadNoData();
        Future.delayed(const Duration(seconds: 1), () {
          refreshController.loadComplete();
        });
      }

      update(["period_pain"]);
    }
  }
}
