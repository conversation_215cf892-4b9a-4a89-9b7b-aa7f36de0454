import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/pages/period/index.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:getx_xiaopa/utils/utils.dart';

class PeriodAnalysisController extends GetxController {
  PeriodAnalysisController();

  List<String> fourStatusLabels = [
    "period_four_bottom_flux_five".tr,
    "period_four_bottom_flux_four".tr,
    "period_four_bottom_flux_three".tr,
    "period_four_bottom_flux_two".tr,
    "period_four_bottom_flux_one".tr
  ];

  List<String> fiveStatusLabels = [
    "period_four_bottom_pain_five".tr,
    "period_four_bottom_pain_four".tr,
    "period_four_bottom_pain_three".tr,
    "period_four_bottom_pain_two".tr,
    "period_four_bottom_pain_one".tr
  ];

  List<PeriodCycleItemModel> periodCycleModelItems = [];

  ///是否准时
  bool isOnTime = false;
  int cycleDay = 0;
  int realDay = 0;
  int diffDay = 0;
  String sPeriodDate = "";
  String ePeriodDate = "";

  List<OneType> oneTypeList = [];

  List<PeriodItemModel> periodItemModelItems = [];
  int twoReadDay = 0;
  List<TwoType> twoTypeList = [];

  List<ThreeType> threeTypeList = [];

  List<FourType> fourTypeList = [];

  List<FourType> fiveTypeList = [];

  @override
  void onReady() async {
    super.onReady();

    await _cycleData();

    await _periodData();

    update(["period_analysis"]);
  }

  ///经期列表的数据处理
  _cycleData() async {
    periodCycleModelItems = await PeriodStore.to.periodCycleItems(isAll: true);

    if (periodCycleModelItems.isNotEmpty) {
      cycleDay = periodCycleModelItems[0].expectedDuration ?? 0;
      sPeriodDate = TimeUtil.getLocaleTimeWithWeek(
          TimeUtil.parseIsoDate(periodCycleModelItems[0].startDate!));
      ePeriodDate = TimeUtil.getLocaleTimeWithWeek(
          TimeUtil.parseIsoDate(periodCycleModelItems[0].endDate!));

      realDay = periodCycleModelItems[0].period!.realDuration ?? 0;
      if (periodCycleModelItems.length >= 2) {
        diffDay = (periodCycleModelItems[1].realDuration ?? 0) -
            (periodCycleModelItems[1].expectedDuration ?? 0);
        isOnTime = diffDay >= 0;
      } else {
        diffDay = (periodCycleModelItems[0].realDuration ?? 0) -
            (periodCycleModelItems[0].expectedDuration ?? 0);
        isOnTime = diffDay >= 0;
      }

      for (var i = 0; i < min(periodCycleModelItems.length, 3); i++) {
        PeriodCycleItemModel cPeriodCycleItemModel = periodCycleModelItems[i];
        OneType oneType = OneType();
        oneType.date = TimeUtil.getLocaleTimeWithWeek(
            TimeUtil.parseIsoDate(cPeriodCycleItemModel.startDate!));
        oneType.pValue = cPeriodCycleItemModel.expectedDuration!.toDouble();
        oneType.pColor = oneType.pValue > 35
            ? const Color.fromRGBO(255, 211, 9, 1)
            : const Color.fromRGBO(254, 139, 172, 1);
        if (i == 0) {
          oneType.cDaysTip = 'period_analysis_one_widget_enght'
              .trArgs(['${oneType.pValue.toInt()}']);
        } else {
          oneType.cDaysTip = 'period_analysis_one_widget_three'
              .trArgs(['${oneType.pValue.toInt()}']);
        }

        if (i < periodCycleModelItems.length - 1) {
          PeriodCycleItemModel pPeriodCycleItemModel =
              periodCycleModelItems[i + 1];
          int temp = (pPeriodCycleItemModel.realDuration ?? 0) -
              (pPeriodCycleItemModel.expectedDuration ?? 0);

          oneType.beforeTips = temp >= 0
              ? 'period_analysis_one_widget_seven'.trArgs(['$temp'])
              : 'period_analysis_one_widget_ten'.trArgs(['${temp.abs()}']);
        } else {
          int temp = (cPeriodCycleItemModel.realDuration ?? 0) -
              (cPeriodCycleItemModel.expectedDuration ?? 0);
          oneType.beforeTips = temp >= 0
              ? 'period_analysis_one_widget_seven'.trArgs(['$temp'])
              : 'period_analysis_one_widget_ten'.trArgs(['${temp.abs()}']);
        }
        oneTypeList.add(oneType);
      }
    }
  }

  ///月经列表的数据处理
  _periodData() async {
    periodItemModelItems = await PeriodStore.to.periodItem(isAll: true);

    if (periodItemModelItems.isNotEmpty) {
      twoReadDay = periodItemModelItems[0].realDuration ?? 0;

      ///构造小花天数widget的数据
      for (var i = 0; i < 6; i++) {
        TwoType twoType = TwoType();
        if (i < periodCycleModelItems.length) {
          PeriodItemModel periodItemModel = periodItemModelItems[i];

          twoType.key = TimeUtil.getLocaleTimeWithWeek(
              TimeUtil.parseIsoDate(periodItemModel.startDate!),
              isEn: true,
              isZero: true);
          twoType.num = periodItemModel.isEnd!
              ? (periodItemModelItems[i].realDuration ?? 0)
              : 0;
        } else {
          twoType.key = '-/-';
          twoType.num = 0;
        }

        twoTypeList.add(twoType);
      }
      twoTypeList = twoTypeList.reversed.toList();

      if (periodItemModelItems[0].records != null) {
        ///构造颜色变化widget的数据
        for (var i = 0; i < (periodItemModelItems[0].realDuration ?? 0); i++) {
          ThreeType threeType = ThreeType();
          for (PeriodItemModelRecords record in periodItemModelItems[0]
              .records!
              .whereType<PeriodItemModelRecords>()) {
            if (record.dayNumber == i + 1) {
              threeType.image = PeriodUtils.getStatusColorImage(
                  int.parse(record.color ?? '0'));
              threeType.colorDesc =
                  PeriodUtils.getStatusColorStr(int.parse(record.color ?? '0'));
              break;
            } else {
              threeType.image = PeriodUtils.getStatusColorImage(0);
              threeType.colorDesc = PeriodUtils.getStatusColorStr(0);
            }
          }
          threeTypeList.add(threeType);
        }

        ///构造流量变化widget的数据
        for (var i = 0; i < (periodItemModelItems[0].realDuration ?? 0); i++) {
          FourType fourType = FourType();
          fourType.day = 'period_analysis_pain_chart_day'.trArgs(['${i + 1}']);
          for (PeriodItemModelRecords record in periodItemModelItems[0]
              .records!
              .whereType<PeriodItemModelRecords>()) {
            if (record.dayNumber == i + 1) {
              fourType.num = int.parse(record.flow ?? '0');
              break;
            } else {
              fourType.num = 0;
            }
          }
          fourTypeList.add(fourType);
        }

        ///构造痛感变化widget的数据
        for (var i = 0; i < (periodItemModelItems[0].realDuration ?? 0); i++) {
          FourType fiveType = FourType();
          fiveType.day = 'period_analysis_pain_chart_day'.trArgs(['${i + 1}']);
          for (PeriodItemModelRecords record in periodItemModelItems[0]
              .records!
              .whereType<PeriodItemModelRecords>()) {
            if (record.dayNumber == i + 1) {
              fiveType.num = int.parse(record.pain ?? '0');
              break;
            } else {
              fiveType.num = 0;
            }
          }
          fiveTypeList.add(fiveType);
        }
      }

      if (threeTypeList.isEmpty) {
        for (var i = 0; i < 6; i++) {
          ThreeType threeType = ThreeType();
          threeType.image = PeriodUtils.getStatusColorImage(0);
          threeType.colorDesc = PeriodUtils.getStatusColorStr(0);
          threeTypeList.add(threeType);
        }
      }
      if (fourTypeList.isEmpty) {
        for (var i = 0; i < 6; i++) {
          FourType fourType = FourType();
          fourType.day = 'period_analysis_pain_chart_day'.trArgs(['${i + 1}']);
          fourType.num = 0;
          fourTypeList.add(fourType);
        }
      }
      if (fiveTypeList.isEmpty) {
        for (var i = 0; i < 6; i++) {
          FourType fiveType = FourType();
          fiveType.day = 'period_analysis_pain_chart_day'.trArgs(['${i + 1}']);
          fiveType.num = 0;
          fiveTypeList.add(fiveType);
        }
      }
    }
  }

  periodCycle() {
    Get.toNamed(AppRoutes.PERIOD_CYCLE);
  }

  periodDay() {
    Get.toNamed(AppRoutes.PERIOD_DAY);
  }

  periodColor() {
    Get.toNamed(AppRoutes.PERIOD_COLOR);
  }

  periodFlux() {
    Get.toNamed(AppRoutes.PERIOD_FLUX);
  }

  periodPain() {
    Get.toNamed(AppRoutes.PERIOD_PAIN);
  }
}

class OneType {
  String date;
  String beforeTips;
  Color pColor;
  double pValue;
  String cDaysTip;

  OneType({
    this.date = '',
    this.beforeTips = '',
    this.pColor = Colors.transparent,
    this.pValue = 0,
    this.cDaysTip = '',
  });
}

class TwoType {
  String key;
  int num;

  TwoType({
    this.key = '',
    this.num = 0,
  });
}

class ThreeType {
  String image;
  String colorDesc;

  ThreeType({
    this.image = '',
    this.colorDesc = '',
  });
}

class FourType {
  String day;
  int num;

  FourType({
    this.day = '',
    this.num = 0,
  });
}
