import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:getx_xiaopa/pages/period/period_analysis/index.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'dart:math';

class ChartWidget extends StatefulWidget {
  final List<FourType> fourTypeList;
  final List<String> statusLabels;

  const ChartWidget({
    super.key,
    required this.statusLabels,
    required this.fourTypeList,
  });

  @override
  State<ChartWidget> createState() => _ChartWidgetState();
}

class _ChartWidgetState extends State<ChartWidget> {
  final List<int> _painLevels = []; // 0-4 对应完全不痛到痛到极致
  final List<String> _dayLabels = []; // 第1天、第2天等

  @override
  void initState() {
    super.initState();
    for (FourType type in widget.fourTypeList) {
      _painLevels.add(type.num);
      _dayLabels.add(type.day);
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 160.h,
      child: Row(
        children: [
          // 左侧固定的痛感等级标签
          SizedBox(
            width: 50.w,
            child: Column(
                children: List.generate(widget.statusLabels.length, (index) {
              return Column(
                children: [
                  _buildLevelLabel(widget.statusLabels[index]),
                  SizedBox(height: 6.5.h),
                ],
              );
            })),
          ),
          SizedBox(width: 10.w),
          // 右侧可滚动的图表区域
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: SizedBox(
                width: _painLevels.length * 50.w, // 每列宽度50
                child: CustomPaint(
                  size: Size(_painLevels.length * 50.w, 150.h),
                  painter: PainChartPainter(
                    painLevels: _painLevels,
                    dayLabels: _dayLabels,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLevelLabel(String text) {
    return Container(
      // height: 30.h,
      alignment: Alignment.centerRight,
      child: Text(
        text,
        style: StyleConfig.otherStyle(
          color: ColorConfig.shopDetailTextColor,
          fontSize: 12,
        ),
      ),
    );
  }
}

class PainChartPainter extends CustomPainter {
  final List<int> painLevels;
  final List<String> dayLabels;

  PainChartPainter({
    required this.painLevels,
    required this.dayLabels,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final columnWidth = size.width / painLevels.length;
    final chartHeight = size.height - 20.h; // 留出底部标签空间
    final levelHeight = chartHeight / 5; // 5个等级

    // 绘制网格线
    _drawGrid(canvas, size, columnWidth, levelHeight);

    // 绘制数据柱
    for (int i = 0; i < painLevels.length; i++) {
      final centerX = columnWidth * i + columnWidth / 2;
      final level = painLevels[i];

      if (level > 0) {
        _drawBar(canvas, centerX, level, levelHeight, chartHeight - 10);
      } else {
        _drawDashedBar(canvas, centerX, levelHeight, chartHeight - 10);
      }

      // 绘制底部标签
      _drawDayLabel(canvas, centerX, chartHeight + 5.h, dayLabels[i]);
    }
  }

  void _drawGrid(
      Canvas canvas, Size size, double columnWidth, double levelHeight) {
    final gridPaint = Paint()
      ..color = const Color.fromRGBO(234, 234, 234, 1)
      ..strokeWidth = 0.5;

    // 绘制水平网格线
    for (int i = 0; i <= 5; i++) {
      double y = i * levelHeight + (5 - 3 * i);

      _drawDashedLine(
        canvas,
        Offset(0, y),
        Offset(size.width, y),
        gridPaint,
        4.0, // dashWidth
        4.0, // dashSpace
      );
    }
  }

  void _drawBar(Canvas canvas, double centerX, int level, double levelHeight,
      double chartHeight) {
    final barWidth = 20.w;
    final barHeight = level * levelHeight;
    final top = chartHeight - barHeight;

    final paint = Paint()
      ..color = const Color.fromRGBO(254, 139, 172, 1)
      ..style = PaintingStyle.fill;

    final rect = RRect.fromRectAndCorners(
      Rect.fromLTWH(centerX - barWidth / 2, top, barWidth, barHeight),
      topLeft: Radius.circular(2.r),
      topRight: Radius.circular(2.r),
    );

    canvas.drawRRect(rect, paint);
  }

  void _drawDashedBar(
      Canvas canvas, double centerX, double levelHeight, double chartHeight) {
    final barWidth = 20.w;
    final barHeight = levelHeight * 0.8; // 虚线柱的高度
    final top = chartHeight - barHeight;

    final rect =
        Rect.fromLTWH(centerX - barWidth / 2, top, barWidth, barHeight);
    _drawDashedRect(canvas, rect);
  }

  void _drawDashedRect(Canvas canvas, Rect rect) {
    final paint = Paint()
      ..color = const Color.fromRGBO(254, 139, 172, 1)
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    const dashWidth = 3.0;
    const dashSpace = 3.0;

    // 绘制四条边的虚线
    _drawDashedLine(
        canvas, rect.topLeft, rect.topRight, paint, dashWidth, dashSpace);
    _drawDashedLine(
        canvas, rect.topRight, rect.bottomRight, paint, dashWidth, dashSpace);
    _drawDashedLine(
        canvas, rect.bottomRight, rect.bottomLeft, paint, dashWidth, dashSpace);
    _drawDashedLine(
        canvas, rect.bottomLeft, rect.topLeft, paint, dashWidth, dashSpace);
  }

  void _drawDashedLine(Canvas canvas, Offset start, Offset end, Paint paint,
      double dashWidth, double dashSpace) {
    final dx = end.dx - start.dx;
    final dy = end.dy - start.dy;
    final distance = sqrt(dx * dx + dy * dy);

    if (distance == 0) return;

    final direction = Offset(dx / distance, dy / distance);
    double currentDistance = 0;
    bool drawDash = true;

    while (currentDistance < distance) {
      final segmentLength = drawDash ? dashWidth : dashSpace;
      final nextDistance =
          (currentDistance + segmentLength).clamp(0.0, distance);

      if (drawDash) {
        final segmentStart = start + direction * currentDistance;
        final segmentEnd = start + direction * nextDistance;
        canvas.drawLine(segmentStart, segmentEnd, paint);
      }

      currentDistance = nextDistance;
      drawDash = !drawDash;
    }
  }

  void _drawDayLabel(Canvas canvas, double centerX, double y, String label) {
    final textPainter = TextPainter(
      text: TextSpan(
        text: label,
        style: StyleConfig.otherStyle(
          color: ColorConfig.authenticationTextColor,
          fontSize: 10,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    textPainter.paint(canvas, Offset(centerX - textPainter.width / 2, y));
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
