import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/pages/period/period_analysis/index.dart';
import 'package:getx_xiaopa/values/values.dart';

class ThreeItem extends StatefulWidget {
  final List<ThreeType> threeTypeList;
  const ThreeItem({super.key, required this.threeTypeList});

  @override
  State<ThreeItem> createState() => _ThreeItemState();
}

class _ThreeItemState extends State<ThreeItem> {
  final List<String> _image = [];
  final List<String> _str = [];

  @override
  void initState() {
    for (ThreeType type in widget.threeTypeList) {
      _str.add(type.colorDesc);
      _image.add(type.image);
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 74.h,
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: List.generate(widget.threeTypeList.length, (index) {
          return Container(
            margin: EdgeInsets.only(left: index > 0 ? 20.w : 0),
            child: Column(
              children: [
                Text(
                  'period_analysis_pain_chart_day'.trArgs(['${index + 1}']),
                  style: StyleConfig.otherStyle(
                      color: ColorConfig.authenticationTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w500),
                ),
                Images(
                  path: _image[index],
                  width: 40.r,
                  height: 40.r,
                ),
                Text(
                  _str[index],
                  style: StyleConfig.otherStyle(
                      color: ColorConfig.shopDetailTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w500),
                ),
              ],
            ),
          );
        }),
      ),
    );
  }
}
