import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/pages/period/index.dart';
import 'package:getx_xiaopa/pages/period/period_analysis/index.dart';
import 'package:getx_xiaopa/utils/utils.dart';

class PeriodCycleController extends BaseListController {
  PeriodCycleController();

  List<PeriodCycleItemModel> periodCycleModelItems = [];
  List<PeriodCycleItemModel> tempList = [];
  List<OneType> oneTypeList = [];
  final int _pageSize = 20;

  @override
  void initData() {
    _cycleData();
  }

  @override
  void onHidden() {}

  @override
  refreshData() {
    super.refreshData();
    page = 1;
    _cycleData();
  }

  @override
  loadMore() {
    super.loadMore();
    if (tempList.isEmpty) {
      refreshController.loadNoData();
      return;
    }
    page++;
    _cycleData();
  }

  _cycleData() async {
    if (page == 1) {
      periodCycleModelItems.clear();
      oneTypeList.clear();
      tempList.clear();
      update(["cycle_record"]);
      tempList = await PeriodStore.to
          .periodCycleItems(isAll: true, page: page, pageSize: _pageSize);
      refreshController.refreshCompleted();
      refreshController.loadComplete();
    } else {
      tempList = await PeriodStore.to
          .periodCycleItems(isAll: true, page: page, pageSize: _pageSize);
      if (tempList.isEmpty) {
        refreshController.loadNoData();
        Future.delayed(const Duration(seconds: 1), () {
          refreshController.loadComplete();
        });
        return;
      }
      refreshController.loadComplete();
    }
    // 创建新列表避免并发修改异常
    final newList = List<PeriodCycleItemModel>.from(periodCycleModelItems);
    newList.addAll(tempList);
    periodCycleModelItems = newList;

    for (var i = 0; i < periodCycleModelItems.length; i++) {
      PeriodCycleItemModel model = periodCycleModelItems[i];
      OneType oneType = OneType();

      oneType.date = TimeUtil.getLocaleTimeWithWeek(
          TimeUtil.parseIsoDate(model.startDate!));
      oneType.pValue = model.expectedDuration!.toDouble();
      oneType.pColor = (oneType.pValue > 35 || oneType.pValue < 21)
          ? const Color.fromRGBO(255, 211, 9, 1)
          : const Color.fromRGBO(254, 139, 172, 1);

      if (i == 0 && page == 1) {
        oneType.cDaysTip = 'period_analysis_one_widget_enght'
            .trArgs(['${oneType.pValue.toInt()}']);
      } else {
        oneType.cDaysTip = 'period_analysis_one_widget_three'
            .trArgs(['${oneType.pValue.toInt()}']);
      }

      if (i < periodCycleModelItems.length - 1) {
        PeriodCycleItemModel pPeriodCycleItemModel =
            periodCycleModelItems[i + 1];
        int temp = (pPeriodCycleItemModel.realDuration ?? 0) -
            (pPeriodCycleItemModel.expectedDuration ?? 0);

        oneType.beforeTips = temp >= 0
            ? 'period_analysis_one_widget_seven'.trArgs(['$temp'])
            : 'period_analysis_one_widget_ten'.trArgs(['${temp.abs()}']);
      } else {
        int temp = (model.realDuration ?? 0) - (model.expectedDuration ?? 0);
        oneType.beforeTips = temp >= 0
            ? 'period_analysis_one_widget_seven'.trArgs(['$temp'])
            : 'period_analysis_one_widget_ten'.trArgs(['${temp.abs()}']);
      }
      oneTypeList.add(oneType);
    }

    if (periodCycleModelItems.length < _pageSize) {
      refreshController.loadNoData();
      Future.delayed(const Duration(seconds: 1), () {
        refreshController.loadComplete();
      });
    }
    netState = NetState.dataSuccessState;
    update(["cycle_record"]);
  }
}
