import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'index.dart';

class PeriodSettingPage extends BaseCommonView<PeriodSettingController> {
  PeriodSettingPage({super.key});

  @override
  double? get leftWidth => 35.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  @override
  bool get extendBodyBehindAppBar => true;

  // 主视图
  Widget _buildView() {
    return ThemeContainerImage(
      fileName: 'period_setting_bg.png',
      conWidget: 1.sw,
      conHeight: 1.sh,
      fit: BoxFit.fill,
      child: Column(
        children: [
          SizedBox(height: 63.h),
          Row(
            children: [
              SizedBox(width: 94.w),
              SegmentedWidget(
                onSegmentAction: (sign) => controller.changeCalandar(sign),
              ),
              SizedBox(width: 40.w),
              Images(
                path: R.period_setting_png,
                width: 20.r,
                height: 20.r,
              ).inkWell(() => controller.toSettingEdit())
            ],
          ),
          SizedBox(height: 35.h),
          Container(
            width: 345.w,
            height: 624.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.r),
              color: Colors.white,
            ),
            child: Stack(
              alignment: AlignmentDirectional.topCenter,
              children: [
                Positioned.fill(
                  child: Offstage(
                    offstage: !controller.isMonth,
                    child: createCommonView(controller, (_) {
                      return CalendarMonthList(
                        dataMap: controller.dataMap,
                        onFirstYearVisible: (year) => controller.loadDate(year),
                      );
                    }, initBuilder: () => const LoadStatusWidget()),
                  ),
                ),
                Positioned.fill(
                  child: Offstage(
                    offstage: controller.isMonth,
                    child: const CalendarYearList(),
                  ),
                ),
                Positioned(
                  bottom: 8.h,
                  child: Container(
                    width: 200.w,
                    height: 48.h,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(24.r),
                        color: const Color.fromRGBO(40, 39, 46, 1)),
                    child: Center(
                      child: ThemeText(
                        dataStr: "period_setting_btn".tr,
                        keyName: 'textColor',
                        fontWeight: FontWeight.w800,
                      ),
                    ),
                  ).inkWell(() => controller.toEdit()),
                )
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 7.h, left: 15.w, right: 15.w),
            child: Row(
              children: [
                Expanded(
                  child: Row(
                    children:
                        List.generate(controller.tipImage.length, (index) {
                      return Container(
                        margin: EdgeInsets.only(left: index == 0 ? 12.w : 18.w),
                        width: 50.w,
                        height: 14.h,
                        child: Row(
                          children: [
                            Images(
                              path: controller.tipImage[index],
                              width: 16.w,
                              height: 11.h,
                            ),
                            SizedBox(width: 2.w),
                            Text(
                              controller.tipStr[index],
                              style: StyleConfig.otherStyle(
                                  color: ColorConfig.shopDetailTextColor,
                                  fontSize: 10),
                            )
                          ],
                        ),
                      );
                    }),
                  ),
                ),
                Images(
                  path: R.mine_right_png,
                  width: 6.w,
                  height: 10.h,
                ).inkWell(() => controller.toExplainPage())
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget buildContent() {
    return GetBuilder<PeriodSettingController>(
      init: PeriodSettingController(),
      id: "period_setting",
      builder: (_) {
        return _buildView();
      },
    );
  }
}
