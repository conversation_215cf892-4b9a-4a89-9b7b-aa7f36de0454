import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:getx_xiaopa/pages/period/period_setting/widgets/calendar_month_item.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:intl/intl.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

class CalendarMonthList extends StatefulWidget {
  final Map<String, Map> dataMap;
  final void Function(int year)? onFirstYearVisible;

  const CalendarMonthList({
    super.key,
    required this.dataMap,
    required this.onFirstYearVisible,
  });

  @override
  State<CalendarMonthList> createState() => _CalendarMonthListState();
}

class _CalendarMonthListState extends State<CalendarMonthList> {
  static const int _totalCount = 200000; // 虚拟总长度
  late final int _centerIndex; // 当前月份对应的 index
  final DateTime _baseMonth = DateTime.now();

  final ItemScrollController _itemScrollController = ItemScrollController();
  final ItemPositionsListener _itemPositionsListener =
      ItemPositionsListener.create();

  final Set<int> _triggeredYears = {}; // 已触发过的年份

  ///1秒后才执行回调，不然一开始初始化ui时候就会回调了
  bool _isCallBack = false;

  @override
  void initState() {
    super.initState();
    _centerIndex = _totalCount ~/ 2;

    // 初始定位到当前月
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(seconds: 1), () {
        _isCallBack = true;
      });

      _itemPositionsListener.itemPositions.addListener(() {
        final positions = _itemPositionsListener.itemPositions.value;
        if (positions.isNotEmpty && _isCallBack) {
          // 找到最上面的月份
          final firstVisible = positions
              .where((p) => p.itemLeadingEdge >= 0)
              .reduce((min, p) =>
                  (p.itemLeadingEdge < min.itemLeadingEdge) ? p : min);

          final month = _monthForIndex(firstVisible.index);
          final year = month.year;

          if (!_triggeredYears.contains(year)) {
            _triggeredYears.add(year);
            int temp = year;
            if (year < _baseMonth.year) {
              temp = year - 1;
            } else {
              temp = year + 1;
            }
            widget.onFirstYearVisible?.call(temp);
          }
        }
      });
    });
  }

  DateTime _monthForIndex(int index) {
    final diff = index - _centerIndex;
    return DateTime(_baseMonth.year, _baseMonth.month + diff, 1);
  }

  /// 返回一个月的所有格子（含空格）
  List<DateTime?> _daysInMonth(DateTime month) {
    final firstDay = DateTime(month.year, month.month, 1);
    final daysInMonth = DateTime(month.year, month.month + 1, 0).day;
    final firstWeekday = firstDay.weekday % 7; // 周日=0
    final totalCells = ((firstWeekday + daysInMonth) <= 35) ? 35 : 42;

    List<DateTime?> result = List<DateTime?>.filled(totalCells, null);
    for (int i = 0; i < daysInMonth; i++) {
      result[firstWeekday + i] = DateTime(month.year, month.month, i + 1);
    }
    return result;
  }

  @override
  Widget build(BuildContext context) {
    return ScrollablePositionedList.builder(
      itemScrollController: _itemScrollController,
      itemPositionsListener: _itemPositionsListener,
      itemCount: _totalCount,
      initialScrollIndex: _centerIndex,
      itemBuilder: (context, index) {
        final date = _monthForIndex(index);
        final days = _daysInMonth(date);

        // 示例：任务区间 9月2号 ~ 9月10号
        DateTime taskStart = DateTime(2025, 9, 2);
        DateTime taskEnd = DateTime(2025, 9, 8);

        DateTime oDateTime = DateTime(2025, 9, 19);

        bool inRange = false;

        ///开始处理
        bool isBegin = false;
        bool isStart = false;
        bool isEnd = false;
        bool isOvulation = false;
        Color tColor = Colors.black;

        return Container(
          padding: const EdgeInsets.all(0),
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Container(
              margin: EdgeInsets.only(top: 16.h, bottom: 18.h, left: 20.w),
              child: Text(
                DateFormat.yMMMM(ConfigStore.to.locale.toString()).format(date),
                style: StyleConfig.otherStyle(
                    color: ColorConfig.searchTextColor,
                    fontSize: 18,
                    fontWeight: FontWeight.w600),
              ),
            ),
            Container(
              margin: EdgeInsets.only(left: 20.w, right: 20.w),
              child: GridView.builder(
                padding: EdgeInsets.zero,
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 7,
                  childAspectRatio: 1, // 宽高比 1:1
                ),
                itemCount: days.length,
                itemBuilder: (context, i) {
                  final day = days[i];
                  if (day == null) return const SizedBox();

                  Map? data = widget.dataMap[
                      '${day.year}-${day.month.toString().padLeft(2, "0")}-${day.day.toString().padLeft(2, "0")}'];

                  if (data != null) {
                    String sStr =
                        TimeUtil.getSimpleDate(data['sTime'], isHMS: false);
                    String eStr =
                        TimeUtil.getSimpleDate(data['eTime'], isHMS: false);
                    String oStr =
                        TimeUtil.getSimpleDate(data['oDate'], isHMS: false);

                    taskStart = TimeUtil.parseIsoDate(sStr);
                    taskEnd = TimeUtil.parseIsoDate(eStr);
                    oDateTime = TimeUtil.parseIsoDate(oStr);

                    isBegin = true;
                  }

                  if (isBegin) {
                    inRange = !day.isBefore(taskStart) && !day.isAfter(taskEnd);

                    isStart = day.isAtSameMomentAs(taskStart);
                    isEnd = day.isAtSameMomentAs(taskEnd);
                  }

                  isOvulation = day.isAtSameMomentAs(oDateTime);
                  tColor =
                      (inRange || isOvulation) ? Colors.white : Colors.black;

                  return CalendarMonthItem(
                    date: day,
                    inRange: inRange,
                    isStart: isStart,
                    isEnd: isEnd,
                    textColor: tColor,
                    isOvulation: isOvulation,
                  );
                },
              ),
            )
          ]),
        );
      },
    );
  }
}
