import 'package:flutter/material.dart';
import 'package:getx_xiaopa/pages/period/period_setting/index.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:intl/intl.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

class CalendarYearList extends StatefulWidget {
  const CalendarYearList({super.key});

  @override
  State<CalendarYearList> createState() => _CalendarYearListState();
}

class _CalendarYearListState extends State<CalendarYearList> {
  static const int _totalCount = 200000; // 虚拟总长度
  final int baseYear = 1970; // 理论起点年份
  late int _initialIndex = 0;
  @override
  void initState() {
    super.initState();
    final currentYear = DateTime.now().year;
    _initialIndex = currentYear - baseYear;
  }

  List<DateTime?> _daysInMonth(DateTime month) {
    final firstDay = DateTime(month.year, month.month, 1);
    final daysInMonth = DateTime(month.year, month.month + 1, 0).day;
    final firstWeekday = firstDay.weekday % 7;
    final totalCells = ((firstWeekday + daysInMonth) <= 35) ? 35 : 42;

    List<DateTime?> result = List<DateTime?>.filled(totalCells, null);
    for (int i = 0; i < daysInMonth; i++) {
      result[firstWeekday + i] = DateTime(month.year, month.month, i + 1);
    }
    return result;
  }

  @override
  Widget build(BuildContext context) {
    return ScrollablePositionedList.builder(
      itemCount: _totalCount,
      initialScrollIndex: _initialIndex,
      itemBuilder: (context, index) {
        final year = baseYear + index;
        final months = List.generate(12, (i) => DateTime(year, i + 1, 1));
        return Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // 年份
            Padding(
              padding: const EdgeInsets.all(8),
              child: Text(
                "$year",
                style: StyleConfig.otherStyle(
                    color: ColorConfig.searchTextColor,
                    fontSize: 18,
                    fontWeight: FontWeight.w700),
              ),
            ),
            // 12 个月份网格，每行 3 个
            GridView.builder(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: months.length,
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
                childAspectRatio: 0.8,
              ),
              itemBuilder: (context, monthIndex) {
                final month = months[monthIndex];
                final days = _daysInMonth(month);

                return Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Column(
                    children: [
                      Text(
                        DateFormat.MMM(ConfigStore.to.locale.toString())
                            .format(month),
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.searchTextColor, fontSize: 18),
                      ),
                      Expanded(
                        child: GridView.builder(
                          padding: EdgeInsets.zero,
                          physics: const NeverScrollableScrollPhysics(),
                          shrinkWrap: true,
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 7,
                                  childAspectRatio: 1,
                                  mainAxisExtent: 18),
                          itemCount: days.length,
                          itemBuilder: (context, i) {
                            final day = days[i];
                            if (day == null) return const SizedBox();
                            // 示例：任务区间 9月2号 ~ 9月10号
                            final taskStart = DateTime(2025, 9, 2);
                            final taskEnd = DateTime(2025, 9, 8);

                            final inRange = !day.isBefore(taskStart) &&
                                !day.isAfter(taskEnd);

                            final isStart = day.isAtSameMomentAs(taskStart);
                            final isEnd = day.isAtSameMomentAs(taskEnd);

                            final Color tColor = inRange
                                ? Colors.white
                                : ColorConfig.searchTextColor;

                            return CalendarYearItem(
                              date: day,
                              inRange: inRange,
                              isStart: isStart,
                              isEnd: isEnd,
                              textColor: tColor,
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }
}


class MonthCalendarPainter extends CustomPainter {
  final DateTime month;
  final DateTime? taskStart;
  final DateTime? taskEnd;
  final TextStyle textStyle;
  final TextStyle highlightTextStyle;
  final Color highlightColor;

  MonthCalendarPainter({
    required this.month,
    this.taskStart,
    this.taskEnd,
    this.textStyle = const TextStyle(fontSize: 12, color: Colors.black),
    this.highlightTextStyle =
        const TextStyle(fontSize: 12, color: Colors.white),
    this.highlightColor = Colors.blue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final double cellWidth = size.width / 7;
    final double cellHeight = size.height / 6;

    final days = _daysInMonth(month);

    final textPainter = TextPainter(
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    );

    for (int i = 0; i < days.length; i++) {
      final DateTime? day = days[i];
      final int row = i ~/ 7;
      final int col = i % 7;

      final double dx = col * cellWidth;
      final double dy = row * cellHeight;

      if (day != null) {
        // 是否在任务区间内
        final inRange = (taskStart != null && taskEnd != null)
            ? !day.isBefore(taskStart!) && !day.isAfter(taskEnd!)
            : false;

        // 绘制背景
        if (inRange) {
          final rect = Rect.fromLTWH(dx, dy, cellWidth, cellHeight);
          final paint = Paint()..color = highlightColor;
          canvas.drawRect(rect, paint);
        }

        // 绘制文字
        textPainter.text = TextSpan(
          text: "${day.day}",
          style: inRange ? highlightTextStyle : textStyle,
        );
        textPainter.layout(maxWidth: cellWidth);

        textPainter.paint(
          canvas,
          Offset(
            dx + (cellWidth - textPainter.width) / 2,
            dy + (cellHeight - textPainter.height) / 2,
          ),
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant MonthCalendarPainter oldDelegate) {
    return oldDelegate.month != month ||
        oldDelegate.taskStart != taskStart ||
        oldDelegate.taskEnd != taskEnd;
  }

  /// 生成 42 格日期（前置空格 + 当月天数）
  List<DateTime?> _daysInMonth(DateTime month) {
    final firstDay = DateTime(month.year, month.month, 1);
    final daysInMonth = DateTime(month.year, month.month + 1, 0).day;
    final firstWeekday = firstDay.weekday % 7;
    final totalCells = ((firstWeekday + daysInMonth) <= 35) ? 35 : 42;

    List<DateTime?> result = List<DateTime?>.filled(totalCells, null);
    for (int i = 0; i < daysInMonth; i++) {
      result[firstWeekday + i] = DateTime(month.year, month.month, i + 1);
    }
    return result;
  }
}
