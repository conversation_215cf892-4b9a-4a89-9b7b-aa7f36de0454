import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/network/model/request_body.dart';
import 'package:getx_xiaopa/pages/period/index.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:getx_xiaopa/utils/utils.dart';

class PeriodSettingController extends BaseCommonController {
  PeriodSettingController();

  bool isMonth = true;
  List<String> tipImage = [
    'assets/images/period_setting_image_01.png',
    'assets/images/period_setting_image_02.png',
    'assets/images/period_setting_image_03.png',
    'assets/images/period_setting_image_04.png',
    'assets/images/period_setting_image_05.png',
  ];

  List<String> tipStr = [
    "period_three_widget_two".tr,
    "period_three_widget_four".tr,
    "period_three_widget_three".tr,
    "period_three_widget_one".tr,
    "period_setting_tip_one".tr
  ];

  String sTime = '';
  String eTime = '';
  List<PeriodCycleItemModel> periodCycleModelItems = [];

  Map<String, Map> dataMap = {};

  @override
  void initData() async {
    int year = DateTime.now().year;
    sTime = '${year - 1}-01-01 00:00:00';
    eTime = '${year + 1}-12-31 23:59:59';

    await _periodCycleData(sTime: sTime, eTime: eTime);
  }

  @override
  void onHidden() {}

  _periodCycleData({required String sTime, required String eTime}) async {
    periodCycleModelItems.addAll(await PeriodStore.to
        .periodCycleItems(isAll: true, pagination: false, filters: [
      Filter(expr: "between", name: "start_date", value: [sTime, eTime])
    ]));

    for (PeriodCycleItemModel model in periodCycleModelItems) {
      DateTime tempDate = TimeUtil.parseIsoDate(model.startDate!);
      String tempKey =
          '${tempDate.year}-${tempDate.month.toString().padLeft(2, "0")}-${tempDate.day.toString().padLeft(2, "0")}';
      if (!dataMap.containsKey(tempKey)) {
        dataMap[tempKey] = {};
      }
      dataMap[tempKey]!["sTime"] = model.period!.startDate ?? '';
      dataMap[tempKey]!["eTime"] = model.period!.endDate ?? '';
      dataMap[tempKey]!["oDate"] = model.ovulationDate ?? '';
      dataMap[tempKey]!["prDate"] = model.period!.realDuration ?? '';
    }

    Future.delayed(const Duration(milliseconds: 300), () {
      netState = NetState.dataSuccessState;
      update(["period_setting"]);
    });
  }

  loadDate(int year) {
    DateTime tempSTime = TimeUtil.parseIsoDate(sTime);
    DateTime tempETime = TimeUtil.parseIsoDate(eTime);
    if (year <= tempETime.year && year >= tempSTime.year) return;
    String tSTime = '$year-01-01 00:00:00';
    String teTime = '$year-12-31 23:59:59';
    _periodCycleData(sTime: tSTime, eTime: teTime);
  }

  changeCalandar(value) {
    isMonth = value == 1 ? true : false;
    update(["period_setting"]);
  }

  toSettingEdit() {
    Get.toNamed(AppRoutes.PERIOD_SETTING_EDIT);
  }

  toEdit() {
    Get.toNamed(AppRoutes.PERIOD_EDIT);
  }

  toExplainPage() {
    Get.toNamed(AppRoutes.PERIOD_EXPLAIN);
  }
}
