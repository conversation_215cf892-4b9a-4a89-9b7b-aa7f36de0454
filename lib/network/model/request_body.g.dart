// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'request_body.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RequestBody _$RequestBodyFromJson(Map<String, dynamic> json) => RequestBody(
      filters: (json['filters'] as List<dynamic>?)
          ?.map((e) =>
              e == null ? null : Filter.fromJson(e as Map<String, dynamic>))
          .toList(),
      orders: (json['orders'] as List<dynamic>?)
          ?.map((e) => Order.fromJson(e as Map<String, dynamic>))
          .toList(),
      pageNum: (json['pageNum'] as num?)?.toInt() ?? 1,
      pageSize: (json['pageSize'] as num?)?.toInt() ?? 10,
      pagination: json['pagination'] as bool? ?? true,
    );

Map<String, dynamic> _$RequestBodyToJson(RequestBody instance) =>
    <String, dynamic>{
      'filters': instance.filters,
      'orders': instance.orders,
      'pageNum': instance.pageNum,
      'pageSize': instance.pageSize,
      'pagination': instance.pagination,
    };

Filter _$FilterFromJson(Map<String, dynamic> json) => Filter(
      expr: json['expr'] as String,
      name: json['name'] as String,
      value: json['value'],
    );

Map<String, dynamic> _$FilterToJson(Filter instance) => <String, dynamic>{
      'expr': instance.expr,
      'name': instance.name,
      'value': instance.value,
    };

Order _$OrderFromJson(Map<String, dynamic> json) => Order(
      expr: json['expr'] as String,
      name: json['name'] as String,
    );

Map<String, dynamic> _$OrderToJson(Order instance) => <String, dynamic>{
      'expr': instance.expr,
      'name': instance.name,
    };
