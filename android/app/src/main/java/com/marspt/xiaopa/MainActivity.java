package com.marspt.xiaopa;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;

import com.marspt.xiaopa.espblufi.EspBlufiUtil;
import com.marspt.xiaopa.tts.ByteAsrUtil;
import com.marspt.xiaopa.tts.ByteTtsUtil;
import com.bytedance.speech.speechengine.SpeechEngineGenerator;

import java.util.Objects;

import io.flutter.embedding.android.FlutterActivity;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.EventChannel;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.MethodChannel.MethodCallHandler;

public class MainActivity extends FlutterActivity implements MethodCallHandler {

    public static EventChannel.EventSink byteAsrEventSink = null;
    public static EventChannel.EventSink byteAsrVolumeEventSink = null;
    public static EventChannel.EventSink byteTtsEventSink = null;
    public static EventChannel.EventSink espBlufiEventSink = null;
    public static EventChannel.EventSink wxEventSink = null;

    public static EventChannel.EventSink xhsEventSink = null;

    ByteAsrUtil asrUtil;
    ByteTtsUtil ttsUtil;
    EspBlufiUtil espBlufiUtil;

    XhsUtil xhsUtil;

    WxUtil wxUtil;

    // 标记：只有用户点了「粘贴」才允许读取
    private static boolean allowReadClipboard = false;

    @Override
    public void configureFlutterEngine(@NonNull FlutterEngine flutterEngine) {

        super.configureFlutterEngine(flutterEngine);

        SpeechEngineGenerator.PrepareEnvironment(this, this.getApplication());

        MethodChannel byteAsrEventChannel = new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), "byte_asr_init_config");
        byteAsrEventChannel.setMethodCallHandler(this);

        EventChannel byteAsrStreamEventChannel = new EventChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), "byte_asr_stream");
        byteAsrStreamEventChannel.setStreamHandler(byteAsrStreamHandler);

        EventChannel byteVolumeEventChannel = new EventChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), "byte_volume_stream");
        byteVolumeEventChannel.setStreamHandler(byteVolumeStreamHandler);


        MethodChannel byteTtsEventChannel = new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), "byte_tts_init_config");
        byteTtsEventChannel.setMethodCallHandler(this);

        EventChannel byteTtsStreamEventChannel = new EventChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), "byte_tts_stream");
        byteTtsStreamEventChannel.setStreamHandler(byteTtsStreamHandler);

        MethodChannel espBlufiEventChannel = new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), "esp_blufi");
        espBlufiEventChannel.setMethodCallHandler(this);

        EventChannel espBlufiStreamEventChannel = new EventChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), "esp_blufi_stream");
        espBlufiStreamEventChannel.setStreamHandler(espBlufiStreamHandler);

        MethodChannel xhsEventChannel = new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), "xhs_init_config");
        xhsEventChannel.setMethodCallHandler(this);

        EventChannel xhsStreamEventChannel = new EventChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), "xhs_sdk_stream");
        xhsStreamEventChannel.setStreamHandler(xhsStreamHandler);

        MethodChannel wxEventChannel = new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), "wx_init_config");
        wxEventChannel.setMethodCallHandler(this);

        EventChannel wxStreamEventChannel = new EventChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), "wx_sdk_stream");
        wxStreamEventChannel.setStreamHandler(wxStreamHandler);

        // 新增 app/clipboard_control 通道
        new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), "app/clipboard_read")
                .setMethodCallHandler((call, result) -> {
                    if ("get".equals(call.method)) {
                        ClipboardManager clipboard = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
                        if (clipboard.hasPrimaryClip()) {
                            ClipData clip = clipboard.getPrimaryClip();
                            if (clip != null && clip.getItemCount() > 0) {
                                CharSequence text = clip.getItemAt(0).coerceToText(this);
                                result.success(text != null ? text.toString() : null);
                                return;
                            }
                        }
                        result.success(null);
                    } else {
                        result.notImplemented();
                    }
                });

    }

    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
        switch (call.method) {
            case "initByteAsr":
                String asrAppId = call.argument("appId");
                String asrToken = call.argument("token");
                String asrCluster = call.argument("cluster");
                asrUtil = ByteAsrUtil.getInstance(this, asrAppId, asrToken, asrCluster);
                asrUtil.initEngine();
                break;
            case "stopByteAsr":
                asrUtil.stopEngine();
                break;
            case "destroyByteAsr":
                asrUtil.unInitEngine();
                break;
            case "byteStartRecord":
                asrUtil.setStreamHandle(byteAsrEventSink, byteAsrVolumeEventSink);
                String isHold = Objects.requireNonNull(call.argument("isHold")).toString();
                asrUtil.startRecord(isHold);
                break;
            case "byteStopRecord":
                asrUtil.stopRecord();
            case "initByteTts":
                String ttsAppId = call.argument("appId");
                String ttsToken = call.argument("token");
                String ttsCluster = call.argument("cluster");
                String ttsPath = call.argument("wavPath");
                ttsUtil = ByteTtsUtil.getInstance(this, ttsAppId, ttsToken, ttsCluster, ttsPath);
                ttsUtil.initEngine();
                break;
            case "stopByteTts":
                ttsUtil.stopByteSpeechStream();
                break;
            case "destroyByteTts":
                ttsUtil.unInitEngine();
                break;
            case "sendContentToByteTts":
                Boolean isFirst = call.argument("isFirst");
                String content = call.argument("content");
                String yinse = call.argument("yinse");
                ttsUtil.setStreamHandle(byteTtsEventSink, yinse);
                if (Boolean.TRUE.equals(isFirst)) {
                    // 首次播放，加载资源
                    ttsUtil.resumePlayback();
                    if (content != null) {
                        ttsUtil.addSentence(content);
                    }
                    ttsUtil.startEngine();
                    ttsUtil.triggerSynthesis();

                } else {
                    if (content != null) {
                        ttsUtil.addSentence(content);
                    }
                }
                break;
            case "initEspBlufi":
                espBlufiUtil = EspBlufiUtil.getInstance(this);
                break;
            case "scanEspBlufi":
                espBlufiUtil.setStreamHandle(espBlufiEventSink);
                espBlufiUtil.scan();
                break;
            case "stopEspBlufi":
                espBlufiUtil.stopScan();
                break;
            case "connectDevice":
                String deviceName = call.argument("deviceName");
                espBlufiUtil.setStreamHandle(espBlufiEventSink);
                espBlufiUtil.connectDevice(deviceName);
                break;
            case "scanWifi":
                espBlufiUtil.setStreamHandle(espBlufiEventSink);
                espBlufiUtil.scanWifi();
                break;
            case "configureWifi":
                String wifiName = call.argument("wifiName");
                String wifiPwd = call.argument("wifiPwd");
                espBlufiUtil.setStreamHandle(espBlufiEventSink);
                espBlufiUtil.configureWifi(wifiName, wifiPwd);
                break;
            case "statusDevice":
                espBlufiUtil.setStreamHandle(espBlufiEventSink);
                espBlufiUtil.checkStatus();
                break;
            case "cloneConnect":
                espBlufiUtil.closeConnect();
                break;
            case "initXhs":
                String appKey = call.argument("appKey");
                xhsUtil = XhsUtil.Companion.getInstance(this);
                Boolean flag = xhsUtil.registerXhs(appKey);
                result.success(flag);
                break;
            case "shareXhs":
                String title = call.argument("title");
                String contentStr = call.argument("content");
                String image = call.argument("image");
                xhsUtil.shareNote(title, contentStr, image);
                break;
            case "supportShareNote":
                var re = xhsUtil.isSupportShareNote();
                result.success(re);
                break;
            case "initWx":
                String appWxKey = call.argument("appKey");
                wxUtil = WxUtil.Companion.getInstance(this, appWxKey);
                Boolean wxFlag = wxUtil.registerWx(appWxKey);
                result.success(wxFlag);
                break;
            case "isInstalledWx":
                var wxRe = wxUtil.isInstalledWx();
                result.success(wxRe);
                break;
            case "shareSession":
                String imagePath = call.argument("imagePath");
                wxUtil.shareWxWithSession(imagePath);
                break;
            case "shareTimeLine":
                String imagePath2 = call.argument("imagePath");
                wxUtil.shareWxWithTimeLine(imagePath2);
                break;
            case "launchCustomerService":
                String corpId = call.argument("corpId");
                String url = call.argument("url");
                Boolean customerFlag = wxUtil.launchCustomerService(corpId, url);
                result.success(customerFlag);
                break;
            case "launchWxPay":
                String appId = call.argument("appId");
                String partnerId = call.argument("partnerId");
                String prepayId = call.argument("prepayId");
                String sign = call.argument("sign");
                String packageValue = call.argument("packageValue");
                String nonceStr = call.argument("nonceStr");
                String timeStamp = call.argument("timeStamp");

                wxUtil.setStreamHandle(wxEventSink);
                wxUtil.launchWxPay(appId,partnerId,prepayId,sign,packageValue,nonceStr,timeStamp);

            default:
                break;
        }
    }


    /**
     * byteSpeech变化
     */
    EventChannel.StreamHandler byteAsrStreamHandler = new EventChannel.StreamHandler() {
        @Override
        public void onListen(Object arguments, EventChannel.EventSink events) {
            byteAsrEventSink = events;
        }

        @Override
        public void onCancel(Object arguments) {
            byteAsrEventSink = null;
        }
    };

    /**
     * 音量变化
     */
    EventChannel.StreamHandler byteVolumeStreamHandler = new EventChannel.StreamHandler() {
        @Override
        public void onListen(Object arguments, EventChannel.EventSink events) {
            byteAsrVolumeEventSink = events;
        }

        @Override
        public void onCancel(Object arguments) {
            byteAsrVolumeEventSink = null;
        }
    };

    /**
     * byteSpeech变化
     */
    EventChannel.StreamHandler byteTtsStreamHandler = new EventChannel.StreamHandler() {
        @Override
        public void onListen(Object arguments, EventChannel.EventSink events) {
            byteTtsEventSink = events;
        }

        @Override
        public void onCancel(Object arguments) {
            byteTtsEventSink = null;
        }
    };

    /**
     * espblufi变化
     */
    EventChannel.StreamHandler espBlufiStreamHandler = new EventChannel.StreamHandler() {
        @Override
        public void onListen(Object arguments, EventChannel.EventSink events) {
            espBlufiEventSink = events;
        }

        @Override
        public void onCancel(Object arguments) {
            espBlufiEventSink = null;
        }
    };

    EventChannel.StreamHandler wxStreamHandler = new EventChannel.StreamHandler() {
        @Override
        public void onListen(Object arguments, EventChannel.EventSink events) {
            wxEventSink = events;
        }

        @Override
        public void onCancel(Object arguments) {
            wxEventSink = null;
        }
    };

    EventChannel.StreamHandler xhsStreamHandler = new EventChannel.StreamHandler() {
        @Override
        public void onListen(Object arguments, EventChannel.EventSink events) {
            xhsEventSink = events;
        }

        @Override
        public void onCancel(Object arguments) {
            xhsEventSink = null;
        }
    };

}

