#!/bin/bash
set -e

PUBSPEC_FILE="pubspec.yaml"
DIST_DIR="dist"

# 创建 dist 目录
mkdir -p "$DIST_DIR"

# 读取当前版本
CURRENT_VERSION=$(grep "version: " "$PUBSPEC_FILE" | awk '{print $2}')
echo "📌 当前 pubspec.yaml 版本: $CURRENT_VERSION"

# 输入新版本号
read -p "请输入新版本号 (例如 1.0.0): " NEW_VERSION_NAME
read -p "请输入新 build code (整数，例如 1): " NEW_VERSION_CODE

# 校验输入
if [[ -z "$NEW_VERSION_NAME" || -z "$NEW_VERSION_CODE" ]]; then
  echo "❌ 输入不能为空"
  exit 1
fi

# -----------------------
# 更新 pubspec.yaml
# -----------------------
sed -i.bak "s/version: .*/version: $NEW_VERSION_NAME+$NEW_VERSION_CODE/" "$PUBSPEC_FILE"
rm "$PUBSPEC_FILE.bak"
echo "✅ pubspec.yaml 已更新 → version: $NEW_VERSION_NAME+$NEW_VERSION_CODE"

# -----------------------
# 更新 iOS Generated.xcconfig
# -----------------------
update_ios_xcconfig() {
    XC_CONFIG="ios/Flutter/Generated.xcconfig"

    if [ ! -f "$XC_CONFIG" ]; then
        echo "⚠️ $XC_CONFIG 不存在，先创建..."
        mkdir -p ios/Flutter
        touch "$XC_CONFIG"
    fi

    echo "📄 更新 $XC_CONFIG ..."
    cat > "$XC_CONFIG" <<EOL
// Flutter iOS build config
FLUTTER_TARGET=lib/main.dart
FLUTTER_BUILD_NAME=$NEW_VERSION_NAME
FLUTTER_BUILD_NUMBER=$NEW_VERSION_CODE
EOL
    echo "✅ iOS Generated.xcconfig 已更新为最新版本号"
}

update_ios_xcconfig

# -----------------------
# 修复 iOS Info.plist 变量引用
# -----------------------
INFO_PLIST="ios/Runner/Info.plist"
if grep -q "CFBundleShortVersionString" "$INFO_PLIST"; then
  SHORT_VERSION=$(grep -A1 "CFBundleShortVersionString" "$INFO_PLIST" | tail -n1 | awk -F'[<>]' '{print $3}')
  BUILD_VERSION=$(grep -A1 "CFBundleVersion" "$INFO_PLIST" | tail -n1 | awk -F'[<>]' '{print $3}')
  if [[ "$SHORT_VERSION" != "\$(FLUTTER_BUILD_NAME)" || "$BUILD_VERSION" != "\$(FLUTTER_BUILD_NUMBER)" ]]; then
    echo "⚠️ 检测到 Info.plist 未使用 Flutter 变量，正在修复..."
    /usr/libexec/PlistBuddy -c "Set :CFBundleShortVersionString \$(FLUTTER_BUILD_NAME)" "$INFO_PLIST"
    /usr/libexec/PlistBuddy -c "Set :CFBundleVersion \$(FLUTTER_BUILD_NUMBER)" "$INFO_PLIST"
    echo "✅ Info.plist 已修复为 Flutter 变量"
  else
    echo "✅ Info.plist 已正确使用 Flutter 变量"
  fi
else
  echo "⚠️ 未检测到 Info.plist 里有 CFBundleShortVersionString/CFBundleVersion，请手动检查"
fi

# -----------------------
# 清理 Flutter 构建缓存
# -----------------------
echo "🧹 执行 flutter clean"
flutter clean
rm -rf build/ios
flutter pub get

# 进入 iOS 目录执行 pod install
echo "📦 执行 iOS CocoaPods 安装..."
cd ios
pod install
cd ..

echo "✅ CocoaPods 安装完成"

# -----------------------
# 选择打包类型
# -----------------------
echo ""
echo "请选择打包类型："
echo "1) 只打包 AAB"
echo "2) 只打包 APK"
echo "3) AAB 和 APK 都打"
read -p "请输入选项 (1/2/3): " BUILD_TYPE

case $BUILD_TYPE in
  1)
    echo "📦 开始打包 AAB ..."
    flutter build appbundle --release
    AAB_PATH="build/app/outputs/bundle/release/app-release.aab"
    cp "$AAB_PATH" "$DIST_DIR/xiaopa-v${NEW_VERSION_NAME}+${NEW_VERSION_CODE}-$(date +%m%d%H%M).aab"
    echo "✅ AAB 已生成并拷贝到 $DIST_DIR"
    ;;
  2)
    echo "📦 开始打包 APK ..."
    flutter build apk --release
    APK_PATH="build/app/outputs/flutter-apk/app-release.apk"
    cp "$APK_PATH" "$DIST_DIR/xiaopa-v${NEW_VERSION_NAME}+${NEW_VERSION_CODE}-$(date +%m%d%H%M).apk"
    echo "✅ APK 已生成并拷贝到 $DIST_DIR"
    ;;
  3)
    echo "📦 开始打包 AAB ..."
    flutter build appbundle --release
    AAB_PATH="build/app/outputs/bundle/release/app-release.aab"
    cp "$AAB_PATH" "$DIST_DIR/xiaopa-v${NEW_VERSION_NAME}+${NEW_VERSION_CODE}-$(date +%m%d%H%M).aab"
    echo "✅ AAB 已生成并拷贝到 $DIST_DIR"

    echo "📦 开始打包 APK ..."
    flutter build apk --release
    APK_PATH="build/app/outputs/flutter-apk/app-release.apk"
    cp "$APK_PATH" "$DIST_DIR/xiaopa-v${NEW_VERSION_NAME}+${NEW_VERSION_CODE}-$(date +%m%d%H%M).apk"
    echo "✅ APK 已生成并拷贝到 $DIST_DIR"
    ;;
  *)
    echo "❌ 无效选择，已退出"
    exit 1
    ;;
esac

# -----------------------
# 可选 iOS 构建
# -----------------------
# read -p "是否同时构建 iOS release？(y/n): " BUILD_IOS
# if [[ "$BUILD_IOS" == "y" || "$BUILD_IOS" == "Y" ]]; then
#   echo "📱 开始打包 iOS release (不签名模式)..."
#   flutter build ios --release --no-codesign
#   IOS_PATH="build/ios/iphoneos/Runner.app"
#   cp -R "$IOS_PATH" "$DIST_DIR/xiaopa-v${NEW_VERSION_NAME}+${NEW_VERSION_CODE}.app"
#   echo "✅ iOS release 已拷贝到 $DIST_DIR"
# fi

# echo "🎉 打包完成！所有产物已放在 $DIST_DIR 目录"